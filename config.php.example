<?php

return [
    'database' => [
        'host' => 'mysql',
        'dbname' => 'appdb',
        'username' => 'appuser',
        'password' => 'userpassword',
        'charset' => 'utf8mb4'
    ],
    'backblaze' => [
        'account_id' => 'your_account_id',
        'archive' => [
            'bucket_name' => 'lightwave-camera-archive',
            'application_key' => 'your_archive_application_key',
            'endpoint' => 'https://s3.us-west-001.backblazeb2.com'
        ],
        'thumbnails' => [
            'bucket_name' => 'lightwave-camera-thumbnails',
            'application_key' => 'your_thumbnails_application_key',
            'endpoint' => 'https://s3.us-west-001.backblazeb2.com'
        ],
        'retention_days' => 180 // 6 months
    ],
    // Legacy screenshot configuration (for backward compatibility)
    'screenshot' => [
        'output_dir' => __DIR__ . '/screenshots',
        'ffmpeg_path' => '/usr/bin/ffmpeg'
    ],

    // Async job processing configuration
    'async_jobs' => [
        // Global settings
        'max_workers' => 6,
        'worker_timeout' => 3600, // 1 hour default
        'job_retry_limit' => 3,
        'cleanup_completed_jobs_after_days' => 7,
        'log_level' => 'info', // debug, info, warning, error, critical
        'log_retention_days' => 30,
        'heartbeat_interval' => 30,
        'worker_check_interval' => 5,

        // Screenshot job configuration
        'screenshot' => [
            'enabled' => true,
            'max_workers' => 6,
            'worker_timeout' => 1800, // 30 minutes
            'output_dir' => __DIR__ . '/screenshots',
            'ffmpeg_path' => '/usr/bin/ffmpeg',
            'cjpeg_path' => '/usr/local/bin/cjpeg',

            // Optimized command template using ffmpeg + cjpeg pipeline
            // Note: Arguments are properly escaped in PHP code, so no quotes needed in template
            'command_template' => '%s -i %s -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg %s && %s -quality 75 -progressive -optimize -nojfif %s > %s && rm %s',

            // Image variation settings
            'image_variations' => [
                'original' => [
                    'enabled' => true
                ],
                'logo_overlay' => [
                    'enabled' => true
                ],
                'thumbnail_720p' => [
                    'enabled' => true,
                    'width' => 1280,
                    'height' => 720
                ],
                'thumbnail_low' => [
                    'enabled' => true,
                    'width' => 720,
                    'height' => 405
                ]
            ],

            // SAFETY: Upload disabled by default
            'upload_to_backblaze' => false,
        ],

        // Video timelapse generation configuration
        'video' => [
            'enabled' => true, // Now implemented
            'max_workers' => 3,
            'worker_timeout' => 7200, // 2 hours for video processing
            'output_dir' => __DIR__ . '/timelapses',
            'temp_dir' => __DIR__ . '/temp/video',
            'ffmpeg_path' => '/usr/bin/ffmpeg',
            'screenshots_dir' => __DIR__ . '/screenshots',

            // FFmpeg command template for timelapse generation
            // %s = ffmpeg_path, %s = framerate, %s = input_pattern, %s = duration, %s = crf, %s = output_file
            // Note: Arguments are properly escaped in PHP code, so no quotes needed in template
            'command_template' => '%s -framerate %d -i %s -t %d -c:v libx264 -preset slower -crf %d -tune stillimage -pix_fmt yuv420p -r 30 -y %s',

            // Timelapse period configurations
            'periods' => [
                '1d' => [
                    'enabled' => true,
                    'name' => 'Hourly Timelapse',
                    'description' => 'Last 24 hours, generated hourly',
                    'source_hours' => 24,
                    'frame_skip' => 1, // Use every frame
                    'framerate' => 30,
                    'crf_quality' => 23, // Good quality
                    'old_videos_days' => 2,
                    'schedule_cron' => '0 * * * *' // Every hour
                ],
                '7d' => [
                    'enabled' => true,
                    'name' => 'Daily Timelapse',
                    'description' => 'Last 7 days, generated daily',
                    'source_hours' => 168, // 7 * 24
                    'frame_skip' => 2, // Skip every 2nd frame
                    'framerate' => 30,
                    'crf_quality' => 25, // Balanced quality/size
                    'old_videos_days' => 7,
                    'schedule_cron' => '0 2 * * *' // Daily at 2 AM
                ],
                '30d' => [
                    'enabled' => true,
                    'name' => 'Monthly Timelapse',
                    'description' => 'Last 30 days, generated daily',
                    'source_hours' => 720, // 30 * 24
                    'frame_skip' => 5, // Skip 4 out of 5 frames
                    'framerate' => 30,
                    'crf_quality' => 25, // Higher compression for longer videos
                    'old_videos_days' => 7,
                    'schedule_cron' => '0 3 * * *' // Daily at 3 AM
                ]
            ],

            // Backblaze upload settings
            'backblaze' => [
                'enabled' => false, // SAFETY: disabled by default
                'bucket_name' => 'lightwave-camera-video-archive',
                'folder_prefix' => 'timelapses/',
                'upload_timeout' => 1800, // 30 minutes
                'retry_attempts' => 3
            ],

            // Performance and optimization settings
            'optimization' => [
                'max_concurrent_ffmpeg' => 2,
                'memory_limit' => '2G',
                'temp_cleanup' => true,
                'parallel_processing' => true
            ]
        ],

        // Future cleanup job configuration
        'cleanup' => [
            'enabled' => false,
            'max_workers' => 1,
            'worker_timeout' => 3600,
            'schedule' => [
                'old_screenshots_days' => 31,
                'old_videos_days' => 31,
                'temp_files_hours' => 24
            ]
        ]
    ],

    // Use this key in the $_GET['key'] parameter to access the test scripts
    'security_key' => 'your_security_key'
]; 