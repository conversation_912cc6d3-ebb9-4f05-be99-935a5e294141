# Disk Audit Script - Technical Specification

## Document Information
- **<PERSON>ript Name**: `disk_audit.sh`
- **Version**: 1.0
- **Author**: System Administration Team
- **Date**: 2025-01-14
- **Purpose**: Monitor disk usage and file growth patterns across multiple filesystems

## 1. Executive Summary

The `disk_audit.sh` script provides comprehensive disk usage monitoring with automated alerting capabilities. It tracks large files across all mounted filesystems, monitors growth patterns over time, and sends email alerts when predefined thresholds are exceeded. The script supports both interactive manual execution and automated cron-based monitoring.

## 2. System Requirements

### 2.1 Operating System Compatibility
- **Primary**: CentOS 7+, RHEL 7+, Ubuntu 18.04+
- **Bash Version**: 4.0 or higher
- **Required Commands**: `find`, `df`, `du`, `mailx`, `awk`, `sort`, `date`

### 2.2 System Dependencies
```bash
# Required packages (install via package manager)
yum install mailx findutils coreutils gawk  # CentOS/RHEL
apt-get install mailutils findutils coreutils gawk  # Ubuntu/Debian
```

### 2.3 Permissions Requirements
- **Read access**: All monitored filesystems
- **Write access**: Script data directory (`/var/lib/disk_audit/` by default)
- **Email capability**: Configured `mailx` with SMTP relay

## 3. Architecture Overview

### 3.1 Core Components
```
disk_audit.sh
├── Configuration Section (lines 1-50)
├── Utility Functions (lines 51-150)
├── Data Management Functions (lines 151-250)
├── Scanning Functions (lines 251-400)
├── Analysis Functions (lines 401-550)
├── Alert Functions (lines 551-700)
├── Output Formatting Functions (lines 701-850)
└── Main Execution Logic (lines 851-1000)
```

### 3.2 Data Flow Architecture
```
[Initial Scan] → [CSV Storage] → [Subsequent Scans] → [Growth Analysis] → [Alert Generation] → [Email Notification]
     ↓              ↓                ↓                    ↓                  ↓                    ↓
[File Discovery] [Historical Data] [Delta Calculation] [Threshold Check] [Alert Tables] [SMTP Delivery]
```

## 4. Configuration Specifications

### 4.1 Primary Configuration Variables
```bash
# File Size Thresholds (in MB)
LARGE_FILE_THRESHOLD_MB=250          # Initial scan minimum file size
GROWTH_PATTERN_THRESHOLD_MB=150      # Cumulative growth for pattern alerts
DAILY_GROWTH_THRESHOLD_MB=300        # 24-hour growth threshold
WEEKLY_GROWTH_THRESHOLD_MB=500       # 7-day growth threshold

# Disk Space Thresholds (in GB)
DISK_SPACE_ALERT_GB=10              # Low disk space alert threshold
DISK_SPACE_CHANGE_GB=20             # Disk space decrease alert threshold

# Data Management
DATA_RETENTION_DAYS=7               # CSV file retention period
CONSECUTIVE_GROWTH_COUNT=3          # Required consecutive growth scans

# Email Configuration
EMAIL_RECIPIENT="<EMAIL>"
EMAIL_SUBJECT_PREFIX="Disk Audit Alert"
EMAIL_SPAM_PREVENTION_HOURS=6       # Minimum hours between low space alerts

# System Paths
DATA_DIR="/var/lib/disk_audit"      # Data storage directory
STATE_FILE="$DATA_DIR/.state"       # State tracking file
LOG_FILE="$DATA_DIR/disk_audit.log" # Optional verbose logging

# Filesystem Exclusions
EXCLUDED_FILESYSTEMS="proc sys dev tmp run"
EXCLUDED_MOUNT_TYPES="tmpfs devtmpfs sysfs proc"
```

### 4.2 Advanced Configuration Options
```bash
# Performance Tuning
MAX_FIND_DEPTH=10                   # Maximum directory traversal depth
FIND_TIMEOUT_SECONDS=3600           # Timeout for find operations
PARALLEL_SCAN_ENABLED=true          # Enable parallel filesystem scanning

# Network Filesystem Handling
NETWORK_FS_TIMEOUT=30               # Timeout for network filesystem operations
SKIP_NETWORK_FS=true                # Skip NFS, CIFS, etc.

# Output Formatting
COLOR_OUTPUT_ENABLED=true           # Enable color output in interactive mode
PROGRESS_INDICATORS=true            # Show progress during long operations
TABLE_WIDTH=120                     # Maximum table width for formatting
```

## 5. Data Storage Specifications

### 5.1 CSV File Format
**Filename Pattern**: `disk_audit_YYYY-MM-DD.csv`
**Location**: `$DATA_DIR/`
**Columns**:
```csv
timestamp,filepath,size_bytes,disk_free_bytes,mount_point,scan_type
2025-01-14T10:30:00,/var/log/messages,524288000,85899345920,/var,initial
2025-01-14T11:30:00,/var/log/messages,536870912,85887063040,/var,rescan
```

### 5.2 State File Format
**Filename**: `.state`
**Format**: Key-value pairs
```bash
last_scan_timestamp=2025-01-14T11:30:00
last_email_low_space_var=2025-01-14T08:00:00
consecutive_growth_/var/log/messages=2
total_files_tracked=1247
```

### 5.3 Data Retention Policy
- **CSV Files**: Automatically deleted after `DATA_RETENTION_DAYS` (default: 7 days)
- **State File**: Persistent, cleaned of obsolete entries during each run
- **Log Files**: Rotated when exceeding 10MB, keep last 3 rotations

## 6. Functional Specifications

### 6.1 Command Line Interface
```bash
# Usage Patterns
./disk_audit.sh                    # Interactive mode - full filesystem scan
./disk_audit.sh -audit             # Automated mode - cron execution with email
./disk_audit.sh -dir /var/log      # Directory-specific monitoring
./disk_audit.sh -v                 # Verbose mode with detailed logging
./disk_audit.sh -h                 # Help and configuration display
./disk_audit.sh --config           # Display current configuration
./disk_audit.sh --test-email       # Test email functionality
```

### 6.2 Execution Modes

#### 6.2.1 Interactive Mode (Default)
- **Trigger**: Direct execution without parameters
- **Behavior**: 
  - Colorized output with progress indicators
  - Real-time display of scan results
  - Human-readable table formatting
  - No email notifications
  - Detailed summary statistics

#### 6.2.2 Automated Mode (`-audit`)
- **Trigger**: `-audit` parameter (designed for cron)
- **Behavior**:
  - Minimal console output (suitable for cron logs)
  - Email alerts when thresholds exceeded
  - Automatic cleanup of old data
  - Exit codes for monitoring integration

#### 6.2.3 Directory-Specific Mode (`-dir <path>`)
- **Trigger**: `-dir` parameter with target directory
- **Behavior**:
  - Scan only specified directory and subdirectories
  - All other functionality identical to full scan
  - Useful for focused monitoring of specific areas

## 7. Scanning Algorithm Specifications

### 7.1 Initial Scan Process
```bash
# Pseudocode for initial scan
for each mounted_filesystem in $(get_mounted_filesystems); do
    if filesystem_not_excluded(mounted_filesystem); then
        find $mounted_filesystem -type f -size +${LARGE_FILE_THRESHOLD_MB}M \
             -not -path "*/proc/*" -not -path "*/sys/*" \
             -exec stat --format="%Y %s %n" {} \; 2>/dev/null |
        while read timestamp size filepath; do
            disk_free=$(df --output=avail "$filepath" | tail -1)
            mount_point=$(df --output=target "$filepath" | tail -1)
            echo "$(date -Iseconds),$filepath,$size,$disk_free,$mount_point,initial" >> $csv_file
        done
    fi
done
```

### 7.2 Subsequent Scan Process
```bash
# Pseudocode for rescan
# 1. Rescan all previously identified files
while IFS=',' read timestamp filepath size_bytes disk_free mount_point scan_type; do
    if [[ -f "$filepath" ]]; then
        current_size=$(stat --format="%s" "$filepath")
        current_disk_free=$(df --output=avail "$filepath" | tail -1)
        echo "$(date -Iseconds),$filepath,$current_size,$current_disk_free,$mount_point,rescan" >> $csv_file
    fi
done < <(get_unique_files_from_history)

# 2. Discover new large files
perform_new_file_discovery()
```

## 8. Alert System Specifications

### 8.1 Alert Categories

#### 8.1.1 Growth Pattern Alert
- **Condition**: Files with ≥3 consecutive scans showing growth AND cumulative growth >150MB
- **Table Columns**: File Path | Current Size | Growth (MB) | Growth (%) | Consecutive Scans
- **Color Coding**: Red background for critical files

#### 8.1.2 24-Hour Alert  
- **Condition**: Files that grew >300MB in the last 24 hours
- **Table Columns**: File Path | Size 24h Ago | Current Size | Growth (MB) | Growth Rate (MB/h)
- **Color Coding**: Orange background for rapid growth

#### 8.1.3 7-Day Alert
- **Condition**: Files that grew >500MB over the last 7 days  
- **Table Columns**: File Path | Size 7d Ago | Current Size | Growth (MB) | Avg Daily Growth
- **Color Coding**: Yellow background for sustained growth

#### 8.1.4 Disk Space Alert
- **Condition**: Available space <10GB OR decreased >20GB in 24h
- **Table Columns**: Mount Point | Current Free | 24h Change | Usage % | Alert Type
- **Color Coding**: Red for critical, orange for warning

### 8.2 Email Alert Specifications

#### 8.2.1 Email Structure
```html
Subject: Disk Audit Alert - [hostname] - [alert_type]

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disk Audit Alert</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .alert-critical { background-color: #ffebee; border-left: 4px solid #f44336; }
        .alert-warning { background-color: #fff3e0; border-left: 4px solid #ff9800; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .size-large { font-weight: bold; color: #d32f2f; }
    </style>
</head>
<body>
    <h2>Disk Audit Alert - [hostname]</h2>
    <p><strong>Alert Time:</strong> [timestamp]</p>
    <p><strong>Alert Type:</strong> [alert_type]</p>
    
    [ALERT_TABLES_CONTENT]
    
    <h3>Summary</h3>
    <ul>
        <li>Total files in alerts: [count]</li>
        <li>Total growth detected: [size]</li>
        <li>Recommended actions: [actions]</li>
    </ul>
    
    <p><em>This is an automated alert from the disk audit system.</em></p>
</body>
</html>
```

#### 8.2.2 Email Delivery Configuration
```bash
# Email delivery function
send_alert_email() {
    local alert_type="$1"
    local html_content="$2"
    local subject="$EMAIL_SUBJECT_PREFIX - $(hostname) - $alert_type"
    
    echo "$html_content" | mailx -a "Content-Type: text/html" \
                                 -s "$subject" \
                                 "$EMAIL_RECIPIENT"
}
```

## 9. Output Formatting Specifications

### 9.1 Table Formatting Standards
Based on existing project patterns, all tables should follow consistent formatting:

```bash
# Table formatting function template
format_table() {
    local title="$1"
    local -a headers=("${@:2}")

    # Calculate column widths dynamically
    local -a col_widths=()
    for header in "${headers[@]}"; do
        col_widths+=(${#header})
    done

    # Print title with decorative border
    echo
    printf "📊 %s\n" "$title"
    printf "═%.0s" $(seq 1 $((${#title} + 4)))
    echo

    # Print headers with proper alignment
    printf "│"
    for i in "${!headers[@]}"; do
        printf " %-${col_widths[$i]}s │" "${headers[$i]}"
    done
    echo

    # Print separator line
    printf "├"
    for width in "${col_widths[@]}"; do
        printf "─%.0s" $(seq 1 $((width + 2)))
        printf "┼"
    done
    echo
}

# Color coding for interactive mode
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
ORANGE='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Size formatting function
format_size() {
    local bytes=$1
    local units=("B" "KB" "MB" "GB" "TB")
    local size=$bytes
    local unit_index=0

    while (( size > 1024 && unit_index < 4 )); do
        size=$((size / 1024))
        ((unit_index++))
    done

    printf "%.1f %s" "$size" "${units[$unit_index]}"
}
```

### 9.2 Progress Indicators
```bash
# Progress indicator for long-running operations
show_progress() {
    local current=$1
    local total=$2
    local operation=$3

    local percent=$((current * 100 / total))
    local filled=$((percent / 2))
    local empty=$((50 - filled))

    printf "\r%s: [" "$operation"
    printf "%*s" $filled | tr ' ' '█'
    printf "%*s" $empty | tr ' ' '░'
    printf "] %d%% (%d/%d)" $percent $current $total
}
```

## 10. Error Handling Specifications

### 10.1 Exit Codes
```bash
# Standard exit codes
EXIT_SUCCESS=0          # Normal completion
EXIT_CONFIG_ERROR=1     # Configuration or parameter error
EXIT_PERMISSION_ERROR=2 # Insufficient permissions
EXIT_DISK_CRITICAL=3    # Critical disk space condition
EXIT_EMAIL_ERROR=4      # Email delivery failure
EXIT_DATA_ERROR=5       # Data corruption or missing files
```

### 10.2 Error Handling Patterns
```bash
# Error handling function template
handle_error() {
    local error_code=$1
    local error_message="$2"
    local line_number=${3:-"unknown"}

    echo "❌ ERROR: $error_message (Line: $line_number)" >&2

    # Log error if verbose mode enabled
    if [[ "$VERBOSE" == "true" ]]; then
        echo "$(date -Iseconds) ERROR: $error_message (Line: $line_number)" >> "$LOG_FILE"
    fi

    # Cleanup temporary files
    cleanup_temp_files

    exit $error_code
}

# Trap for unexpected errors
trap 'handle_error $EXIT_DATA_ERROR "Unexpected error occurred" $LINENO' ERR
```

## 11. Performance Specifications

### 11.1 Performance Requirements
- **Initial Scan**: Complete within 30 minutes for filesystems up to 10TB
- **Subsequent Scans**: Complete within 10 minutes for up to 10,000 tracked files
- **Memory Usage**: Maximum 512MB RAM during execution
- **CPU Usage**: Average <25% of single core during scan operations

### 11.2 Optimization Strategies
```bash
# Parallel processing for multiple filesystems
scan_filesystems_parallel() {
    local -a pids=()
    local max_parallel=4

    for filesystem in "${filesystems[@]}"; do
        # Wait if we've reached max parallel processes
        while (( ${#pids[@]} >= max_parallel )); do
            for i in "${!pids[@]}"; do
                if ! kill -0 "${pids[$i]}" 2>/dev/null; then
                    unset "pids[$i]"
                fi
            done
            pids=("${pids[@]}")  # Reindex array
            sleep 1
        done

        # Start new scan process
        scan_single_filesystem "$filesystem" &
        pids+=($!)
    done

    # Wait for all processes to complete
    for pid in "${pids[@]}"; do
        wait "$pid"
    done
}
```

## 12. Security Specifications

### 12.1 Security Considerations
- **File Permissions**: Script should run with minimal required privileges
- **Path Validation**: All file paths must be validated to prevent injection attacks
- **Email Security**: Email content must be sanitized to prevent header injection
- **Data Protection**: CSV files should have restricted permissions (600)

### 12.2 Security Implementation
```bash
# Path validation function
validate_path() {
    local path="$1"

    # Check for path traversal attempts
    if [[ "$path" =~ \.\./|\.\.\\ ]]; then
        handle_error $EXIT_CONFIG_ERROR "Invalid path detected: $path"
    fi

    # Ensure path is absolute
    if [[ "${path:0:1}" != "/" ]]; then
        handle_error $EXIT_CONFIG_ERROR "Path must be absolute: $path"
    fi

    # Check if path exists and is readable
    if [[ ! -r "$path" ]]; then
        handle_error $EXIT_PERMISSION_ERROR "Cannot read path: $path"
    fi
}

# Email content sanitization
sanitize_email_content() {
    local content="$1"

    # Remove potential email header injection characters
    content="${content//[$'\r\n']/}"
    content="${content//[<>]/}"

    echo "$content"
}
```

## 13. Testing Specifications

### 13.1 Unit Testing Framework
```bash
# Simple testing framework for bash
run_tests() {
    local test_count=0
    local passed_count=0

    echo "🧪 Running disk_audit.sh test suite..."

    # Test configuration validation
    test_config_validation
    ((test_count++))

    # Test file size formatting
    test_size_formatting
    ((test_count++))

    # Test path validation
    test_path_validation
    ((test_count++))

    # Test CSV parsing
    test_csv_parsing
    ((test_count++))

    echo "📊 Test Results: $passed_count/$test_count tests passed"

    if (( passed_count == test_count )); then
        echo "✅ All tests passed!"
        return 0
    else
        echo "❌ Some tests failed!"
        return 1
    fi
}

# Individual test functions
test_size_formatting() {
    local result
    result=$(format_size 1073741824)  # 1GB in bytes

    if [[ "$result" == "1.0 GB" ]]; then
        echo "✅ Size formatting test passed"
        ((passed_count++))
    else
        echo "❌ Size formatting test failed: expected '1.0 GB', got '$result'"
    fi
}
```

## 14. Deployment Specifications

### 14.1 Installation Process
```bash
#!/bin/bash
# Installation script for disk_audit.sh

# Create data directory
sudo mkdir -p /var/lib/disk_audit
sudo chmod 755 /var/lib/disk_audit

# Copy script to system location
sudo cp disk_audit.sh /usr/local/bin/
sudo chmod +x /usr/local/bin/disk_audit.sh

# Create log rotation configuration
sudo tee /etc/logrotate.d/disk_audit << EOF
/var/lib/disk_audit/disk_audit.log {
    weekly
    rotate 4
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
}
EOF

# Setup cron job for automated monitoring
(crontab -l 2>/dev/null; echo "0 * * * * /usr/local/bin/disk_audit.sh -audit") | crontab -

echo "✅ disk_audit.sh installed successfully"
echo "📧 Configure email settings in the script before first run"
```

### 14.2 Cron Configuration
```bash
# Recommended cron schedule
# Hourly automated monitoring
0 * * * * /usr/local/bin/disk_audit.sh -audit >> /var/log/disk_audit_cron.log 2>&1

# Daily comprehensive scan (optional, for high-activity systems)
0 2 * * * /usr/local/bin/disk_audit.sh -audit -full >> /var/log/disk_audit_daily.log 2>&1

# Weekly cleanup and maintenance
0 3 * * 0 /usr/local/bin/disk_audit.sh --cleanup >> /var/log/disk_audit_maintenance.log 2>&1
```
