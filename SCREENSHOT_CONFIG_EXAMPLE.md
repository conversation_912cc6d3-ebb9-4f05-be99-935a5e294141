# Screenshot Job Worker - Configuration Example

## Dynamic Image Variations Configuration

The `ScreenshotJobWorker` now supports dynamic thumbnail variations through configuration. Instead of hardcoding variation types, you can define any number of image variations in your config.

### Configuration Structure

```php
'async_jobs' => [
    'screenshot' => [
        'image_variations' => [
            'logo_overlay' => [
                'enabled' => true
            ],
            'thumbnail_720p' => [
                'enabled' => true,
                'width' => 1280,
                'height' => 720
            ],
            'thumbnail_low' => [
                'enabled' => true, 
                'width' => 640,
                'height' => 360
            ],
            'thumbnail_medium' => [
                'enabled' => false,
                'width' => 854,
                'height' => 480
            ],
            'thumbnail_4k' => [
                'enabled' => false,
                'width' => 3840,
                'height' => 2160
            ],
            'custom_square' => [
                'enabled' => true,
                'width' => 500,
                'height' => 500
            ]
        ]
    ]
]
```

### Key Features

1. **Dynamic Variations**: Add any number of thumbnail variations with custom names
2. **Enable/Disable**: Each variation can be individually enabled or disabled
3. **Custom Dimensions**: Set any width and height for each variation
4. **Validation**: Missing width/height values are automatically detected and logged
5. **Backwards Compatible**: Existing configs with `thumbnail_720p` and `thumbnail_low` continue to work

### Generated Files

For camera ID 1 with timestamp 1234567890, the following files would be generated:

```
screenshots/camera_1/
├── camera_1_1234567890.jpg (original screenshot)
├── logo_overlay_1_1234567890.jpg (if logo_overlay.enabled = true)
├── thumbnail_720p_1_1234567890.jpg (if thumbnail_720p.enabled = true)
├── thumbnail_low_1_1234567890.jpg (if thumbnail_low.enabled = true)
└── custom_square_1_1234567890.jpg (if custom_square.enabled = true)
```

### Notes

- `logo_overlay` is handled separately and doesn't require width/height
- Only variations with `enabled: true` and valid width/height will be processed
- Invalid configurations are logged as warnings but don't stop the process
- All thumbnail variations use the logo overlay as source (if available), otherwise the original screenshot 