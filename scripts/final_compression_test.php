<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../src/ImageCompressor.php';

use App\ImageCompressor;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set max execution time to 30 minutes
set_time_limit(1800);

error_log("=== FINAL COMPREHENSIVE COMPRESSION TEST ===");
echo "=== FINAL COMPREHENSIVE COMPRESSION TEST ===\n";

try {
    $config = require __DIR__ . '/../config.php';
    $ffmpegPath = $config['screenshot']['ffmpeg_path'];
    
    // Test configurations for different compression methods
    $compressionTests = [
        'jpeg_baseline' => [
            'name' => 'Baseline JPEG (q=12)',
            'format' => 'jpeg',
            'options' => ['jpeg_quality' => 12], // Medium compression
            'extension' => '.jpg',
            'post_process' => false
        ],
        'jpeg_optimized' => [
            'name' => 'Optimized JPEG (q=18)',
            'format' => 'jpeg', 
            'options' => ['jpeg_quality' => 18], // Higher compression
            'extension' => '.jpg',
            'post_process' => false
        ],
        'jpeg_turbo_high' => [
            'name' => 'TurboJPEG High Quality',
            'format' => 'jpeg_turbo',
            'options' => ['quality' => 85], // High quality
            'extension' => '.jpg',
            'post_process' => false
        ],
        'jpeg_turbo_medium' => [
            'name' => 'TurboJPEG Medium',
            'format' => 'jpeg_turbo',
            'options' => ['quality' => 75], // Medium quality
            'extension' => '.jpg',
            'post_process' => false
        ],
        'jpeg_turbo_compressed' => [
            'name' => 'TurboJPEG Compressed',
            'format' => 'jpeg_turbo',
            'options' => ['quality' => 60], // Higher compression
            'extension' => '.jpg',
            'post_process' => false
        ],
        'jpeg_optimized_post' => [
            'name' => 'JPEG + jpegoptim',
            'format' => 'jpeg',
            'options' => ['jpeg_quality' => 15],
            'extension' => '.jpg',
            'post_process' => true
        ],
        'jpegtran_lossless' => [
            'name' => 'Jpegtran Lossless',
            'format' => 'jpegtran',
            'options' => [],
            'extension' => '.jpg',
            'post_process' => false
        ],
        'webp_efficient' => [
            'name' => 'WebP Efficient',
            'format' => 'webp',
            'options' => ['quality' => 70, 'compression_level' => 6],
            'extension' => '.webp',
            'post_process' => false
        ],
        'webp_balanced' => [
            'name' => 'WebP Balanced',
            'format' => 'webp',
            'options' => ['quality' => 80, 'compression_level' => 4],
            'extension' => '.webp',
            'post_process' => false
        ],
        'avif_fast' => [
            'name' => 'AVIF Fast',
            'format' => 'avif',
            'options' => ['crf' => 32, 'preset' => 8], // Faster, lower quality
            'extension' => '.avif',
            'post_process' => false
        ],
        'avif_balanced' => [
            'name' => 'AVIF Balanced',
            'format' => 'avif',
            'options' => ['crf' => 30, 'preset' => 6], // Balanced
            'extension' => '.avif',
            'post_process' => false
        ],
        'avif_quality' => [
            'name' => 'AVIF High Quality',
            'format' => 'avif',
            'options' => ['crf' => 28, 'preset' => 4], // Higher quality, slower
            'extension' => '.avif',
            'post_process' => false
        ]
    ];
    
    // Directories
    $sourceFramesDir = __DIR__ . '/../timelapse/performance_test/frames_1008';
    $testDir = __DIR__ . '/../timelapse/compression_test';
    $outputDir = $testDir . '/final_test';
    
    if (!file_exists($outputDir)) {
        mkdir($outputDir, 0755, true);
    }
    
    // Get source frames - only use first 20 for testing
    $allSourceFrames = glob($sourceFramesDir . '/image_*.jpg');
    if (empty($allSourceFrames)) {
        throw new Exception("No source frames found in: " . $sourceFramesDir);
    }
    sort($allSourceFrames);
    
    $sourceFrames = array_slice($allSourceFrames, 0, 20); // Only 20 images
    $totalFrames = count($sourceFrames);
    
    error_log("Using {$totalFrames} source frames for final testing");
    echo "Using {$totalFrames} source frames for final testing\n\n";
    
    // Calculate original total size
    $originalTotalSize = 0;
    $originalSizes = [];
    foreach ($sourceFrames as $frame) {
        $size = filesize($frame);
        $originalTotalSize += $size;
        $originalSizes[] = $size;
    }
    
    $avgOriginalSize = $originalTotalSize / $totalFrames;
    
    error_log("Original total size: " . round($originalTotalSize / 1024, 2) . " KB");
    error_log("Average original size: " . round($avgOriginalSize / 1024, 2) . " KB");
    echo "Original total size: " . round($originalTotalSize / 1024, 2) . " KB\n";
    echo "Average original size: " . round($avgOriginalSize / 1024, 2) . " KB\n\n";
    
    $testResults = [];
    
    foreach ($compressionTests as $testKey => $testConfig) {
        error_log("=== TESTING: {$testConfig['name']} ===");
        echo "=== TESTING: {$testConfig['name']} ===\n";
        
        $testStartTime = microtime(true);
        $compressedFramesDir = $outputDir . '/' . $testKey . '_frames';
        
        if (!file_exists($compressedFramesDir)) {
            mkdir($compressedFramesDir, 0755, true);
        }
        
        // Phase 1: Compress all frames
        error_log("Phase 1: Compressing {$totalFrames} frames...");
        echo "Phase 1: Compressing {$totalFrames} frames...\n";
        
        $compressionStartTime = microtime(true);
        $compressedTotalSize = 0;
        $compressionErrors = 0;
        $individualCompressionRatios = [];
        
        foreach ($sourceFrames as $index => $sourceFrame) {
            try {
                $frameNumber = $index + 1;
                $outputFramePath = $compressedFramesDir . '/image_' . sprintf('%05d', $frameNumber) . $testConfig['extension'];
                
                // Build compression command
                $command = buildFinalCompressionCommand($sourceFrame, $outputFramePath, $testConfig['format'], $testConfig['options'], $ffmpegPath);
                
                exec($command, $output, $returnCode);
                
                if ($returnCode !== 0 || !file_exists($outputFramePath)) {
                    error_log("Failed to compress frame {$frameNumber} with command: {$command}");
                    $compressionErrors++;
                    continue;
                }
                
                // Post-process with jpegoptim if enabled
                if ($testConfig['post_process'] && $testConfig['format'] === 'jpeg') {
                    $optimizeCommand = "jpegoptim --max=85 --strip-all " . escapeshellarg($outputFramePath);
                    exec($optimizeCommand);
                }
                
                $compressedSize = filesize($outputFramePath);
                $compressedTotalSize += $compressedSize;
                
                // Track individual compression ratios
                $individualRatio = 1 - ($compressedSize / $originalSizes[$index]);
                $individualCompressionRatios[] = $individualRatio;
                
                // Progress update every 5 frames
                if ($frameNumber % 5 == 0) {
                    $progress = round(($frameNumber / $totalFrames) * 100, 1);
                    error_log("Compressed {$frameNumber}/{$totalFrames} frames ({$progress}%) - Current avg ratio: " . round(array_sum($individualCompressionRatios) / count($individualCompressionRatios) * 100, 1) . "%");
                    echo "  Progress: {$frameNumber}/{$totalFrames} frames ({$progress}%)\n";
                }
                
            } catch (Exception $e) {
                error_log("Error compressing frame {$frameNumber}: " . $e->getMessage());
                $compressionErrors++;
            }
        }
        
        $compressionTime = microtime(true) - $compressionStartTime;
        $compressionRatio = 1 - ($compressedTotalSize / $originalTotalSize);
        $avgCompressedSize = ($totalFrames - $compressionErrors) > 0 ? 
            $compressedTotalSize / ($totalFrames - $compressionErrors) : 0;
        
        error_log("Compression completed in " . round($compressionTime, 2) . " seconds");
        error_log("Compressed size: " . round($compressedTotalSize / 1024, 2) . " KB");
        error_log("Average compressed size: " . round($avgCompressedSize / 1024, 2) . " KB");
        error_log("Compression ratio: " . round($compressionRatio * 100, 1) . "%");
        error_log("Compression errors: {$compressionErrors}");
        
        echo "  Compression time: " . round($compressionTime, 2) . " seconds\n";
        echo "  Compressed size: " . round($compressedTotalSize / 1024, 2) . " KB\n";
        echo "  Average compressed size: " . round($avgCompressedSize / 1024, 2) . " KB\n";
        echo "  Compression ratio: " . round($compressionRatio * 100, 1) . "%\n";
        echo "  Errors: {$compressionErrors}\n\n";
        
        // Skip video generation if no frames were compressed
        if ($compressionErrors >= $totalFrames) {
            error_log("Skipping video generation - all frames failed to compress");
            echo "  ERROR: All frames failed to compress - skipping video generation\n\n";
            continue;
        }
        
        // Phase 2: Generate video from compressed frames (only from first 20 actual frames)
        error_log("Phase 2: Generating video from compressed frames...");
        echo "Phase 2: Generating video from compressed frames...\n";
        
        $videoStartTime = microtime(true);
        $videoOutputPath = $outputDir . '/' . $testKey . '_video.mp4';
        
        // Handle AVIF specially - convert to JPEG first
        if ($testConfig['format'] === 'avif') {
            $success = createVideoFromAVIF($compressedFramesDir, $videoOutputPath, $ffmpegPath);
        } else {
            $success = createVideoFromFrames($compressedFramesDir, $videoOutputPath, $testConfig['extension'], $ffmpegPath);
        }
        
        $videoTime = microtime(true) - $videoStartTime;
        
        if (!$success || !file_exists($videoOutputPath)) {
            error_log("Failed to create video for {$testConfig['name']}");
            echo "  ERROR: Failed to create video\n\n";
            $videoSize = 0;
            $videoDiagnostics = "Video generation failed";
        } else {
            $videoSize = filesize($videoOutputPath);
            
            // Get video diagnostics
            exec("ffprobe -v quiet -show_entries stream=duration,nb_frames,r_frame_rate -select_streams v:0 " . escapeshellarg($videoOutputPath), $probeOutput);
            $videoDiagnostics = implode(" | ", $probeOutput);
            
            error_log("Video created in " . round($videoTime, 2) . " seconds");
            error_log("Video size: " . round($videoSize / 1024, 2) . " KB");
            error_log("Video diagnostics: " . $videoDiagnostics);
            
            echo "  Video creation time: " . round($videoTime, 2) . " seconds\n";
            echo "  Video size: " . round($videoSize / 1024, 2) . " KB\n";
            echo "  Video diagnostics: " . $videoDiagnostics . "\n\n";
        }
        
        $totalTestTime = microtime(true) - $testStartTime;
        
        // Store results
        $testResults[$testKey] = [
            'name' => $testConfig['name'],
            'format' => $testConfig['format'],
            'compression_time' => $compressionTime,
            'video_creation_time' => $videoTime,
            'total_time' => $totalTestTime,
            'original_frames_size' => $originalTotalSize,
            'compressed_frames_size' => $compressedTotalSize,
            'avg_original_size' => $avgOriginalSize,
            'avg_compressed_size' => $avgCompressedSize,
            'compression_ratio' => $compressionRatio,
            'video_size' => $videoSize,
            'compression_errors' => $compressionErrors,
            'video_diagnostics' => $videoDiagnostics,
            'successful_frames' => $totalFrames - $compressionErrors
        ];
        
        error_log("Total test time for {$testConfig['name']}: " . round($totalTestTime, 2) . " seconds");
        echo "Total test time: " . round($totalTestTime, 2) . " seconds\n";
        echo "=" . str_repeat("=", 60) . "\n\n";
    }
    
    // Generate comprehensive report
    error_log("=== FINAL COMPREHENSIVE TEST RESULTS ===");
    echo "=== FINAL COMPREHENSIVE TEST RESULTS ===\n\n";
    
    // Sort results by compression ratio (best compression first)
    uasort($testResults, function($a, $b) {
        return $b['compression_ratio'] <=> $a['compression_ratio'];
    });
    
    error_log("Results sorted by compression efficiency:");
    echo "Results sorted by compression efficiency:\n\n";
    
    foreach ($testResults as $testKey => $result) {
        if ($result['successful_frames'] == 0) continue; // Skip failed tests
        
        error_log("--- {$result['name']} ---");
        error_log("Compression ratio: " . round($result['compression_ratio'] * 100, 1) . "%");
        error_log("Average reduction: " . round(($result['avg_original_size'] - $result['avg_compressed_size']) / 1024, 1) . " KB per frame");
        error_log("Compression time: " . round($result['compression_time'], 2) . "s");
        error_log("Video creation time: " . round($result['video_creation_time'], 2) . "s");
        error_log("Total time: " . round($result['total_time'], 2) . "s");
        error_log("Speed: " . round($result['successful_frames'] / $result['compression_time'], 1) . " frames/second");
        
        echo "--- {$result['name']} ---\n";
        echo "Compression ratio: " . round($result['compression_ratio'] * 100, 1) . "%\n";
        echo "Average reduction: " . round(($result['avg_original_size'] - $result['avg_compressed_size']) / 1024, 1) . " KB per frame\n";
        echo "Compression time: " . round($result['compression_time'], 2) . "s (" . round($result['successful_frames'] / $result['compression_time'], 1) . " fps)\n";
        echo "Video creation time: " . round($result['video_creation_time'], 2) . "s\n";
        echo "Total time: " . round($result['total_time'], 2) . "s\n";
        echo "Compressed frames: " . round($result['compressed_frames_size'] / 1024, 2) . " KB\n";
        echo "Video size: " . round($result['video_size'] / 1024, 2) . " KB\n";
        echo "Errors: " . $result['compression_errors'] . "\n\n";
    }
    
    // Best performer analysis
    $validResults = array_filter($testResults, function($r) { return $r['successful_frames'] > 0; });
    
    if (!empty($validResults)) {
        $bestCompression = array_keys($validResults)[0];
        $fastestCompression = null;
        $bestSpeed = 0;
        
        foreach ($validResults as $key => $result) {
            $speed = $result['successful_frames'] / $result['compression_time'];
            if ($speed > $bestSpeed) {
                $bestSpeed = $speed;
                $fastestCompression = $key;
            }
        }
        
        error_log("=== FINAL RECOMMENDATIONS ===");
        error_log("🏆 Best compression: {$testResults[$bestCompression]['name']} ({$bestCompression})");
        error_log("📊 Best compression ratio: " . round($testResults[$bestCompression]['compression_ratio'] * 100, 1) . "%");
        error_log("⚡ Fastest compression: {$testResults[$fastestCompression]['name']} ({$fastestCompression})");
        error_log("🏃 Best speed: " . round($bestSpeed, 1) . " frames/second");
        
        echo "=== FINAL RECOMMENDATIONS ===\n";
        echo "🏆 Best compression: {$testResults[$bestCompression]['name']} ({$bestCompression})\n";
        echo "📊 Best compression ratio: " . round($testResults[$bestCompression]['compression_ratio'] * 100, 1) . "%\n";
        echo "⚡ Fastest compression: {$testResults[$fastestCompression]['name']} ({$fastestCompression})\n";
        echo "🏃 Best speed: " . round($bestSpeed, 1) . " frames/second\n\n";
        
        // Calculate storage savings for the timelapse system
        $framesPerDay = 288;
        $cameras = 5;
        $days = 30;
        $totalSystemFrames = $framesPerDay * $cameras * $days;
        
        $originalSystemSize = ($testResults[$bestCompression]['avg_original_size'] * $totalSystemFrames) / 1024 / 1024 / 1024; // GB
        $bestCompressedSystemSize = ($testResults[$bestCompression]['avg_compressed_size'] * $totalSystemFrames) / 1024 / 1024 / 1024; // GB
        $systemSavings = $originalSystemSize - $bestCompressedSystemSize;
        
        error_log("=== SYSTEM-WIDE IMPACT (5 cameras, 30 days, {$totalSystemFrames} frames) ===");
        error_log("Original system storage: " . round($originalSystemSize, 2) . " GB");
        error_log("With best compression: " . round($bestCompressedSystemSize, 2) . " GB");
        error_log("Total savings: " . round($systemSavings, 2) . " GB (" . round(($systemSavings / $originalSystemSize) * 100, 1) . "%)");
        
        echo "=== SYSTEM-WIDE IMPACT (5 cameras, 30 days, " . number_format($totalSystemFrames) . " frames) ===\n";
        echo "Original system storage: " . round($originalSystemSize, 2) . " GB\n";
        echo "With best compression: " . round($bestCompressedSystemSize, 2) . " GB\n";
        echo "Total savings: " . round($systemSavings, 2) . " GB (" . round(($systemSavings / $originalSystemSize) * 100, 1) . "%)\n\n";
        
        echo "=== IMPLEMENTATION RECOMMENDATIONS ===\n";
        echo "1. Use '{$testResults[$bestCompression]['name']}' for maximum storage savings\n";
        echo "2. Use '{$testResults[$fastestCompression]['name']}' for fastest processing\n";
        echo "3. Expected processing time for daily batch: " . round(($framesPerDay * $cameras) / $bestSpeed / 60, 1) . " minutes\n";
        echo "4. Storage savings per day: " . round($systemSavings / 30, 2) . " GB\n";
    }
    
    error_log("Final compression test completed successfully!");
    echo "\nFinal compression test completed successfully!\n";
    
} catch (Exception $e) {
    error_log("Error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo "Error: " . $e->getMessage() . "\n";
}

function buildFinalCompressionCommand($inputPath, $outputPath, $format, $options, $ffmpegPath) {
    switch ($format) {
        case 'avif':
            $crf = $options['crf'] ?? 30;
            $preset = $options['preset'] ?? 6;
            $command = sprintf(
                '%s -y -i %s -c:v libsvtav1 -crf %d -preset %d -pix_fmt yuv420p10le %s 2>/dev/null',
                escapeshellcmd($ffmpegPath),
                escapeshellarg($inputPath),
                $crf,
                $preset,
                escapeshellarg($outputPath)
            );
            break;
            
        case 'webp':
            $quality = $options['quality'] ?? 85;
            $compression_level = $options['compression_level'] ?? 6;
            $command = sprintf(
                '%s -y -i %s -c:v libwebp -quality %d -compression_level %d %s 2>/dev/null',
                escapeshellcmd($ffmpegPath),
                escapeshellarg($inputPath),
                $quality,
                $compression_level,
                escapeshellarg($outputPath)
            );
            break;
            
        case 'jpeg_turbo':
            // Use jpegtran for lossless optimization instead of djpeg|cjpeg
            $quality = $options['quality'] ?? 75;
            if ($quality >= 90) {
                // For high quality, use jpegtran (lossless optimization)
                $command = sprintf(
                    'jpegtran -optimize -progressive -outfile %s %s',
                    escapeshellarg($outputPath),
                    escapeshellarg($inputPath)
                );
            } else {
                // For lower quality, use proper djpeg | cjpeg pipeline syntax
                $command = sprintf(
                    'djpeg %s | cjpeg -quality %d -optimize -progressive > %s',
                    escapeshellarg($inputPath),
                    $quality,
                    escapeshellarg($outputPath)
                );
            }
            break;
            
        case 'jpegtran':
            // Use jpegtran for pure lossless optimization
            $command = sprintf(
                'jpegtran -optimize -progressive -outfile %s %s',
                escapeshellarg($outputPath),
                escapeshellarg($inputPath)
            );
            break;
            
        case 'jpeg':
        default:
            $quality = $options['jpeg_quality'] ?? 15;
            $command = sprintf(
                '%s -y -i %s -c:v mjpeg -q:v %d -huffman optimal %s 2>/dev/null',
                escapeshellcmd($ffmpegPath),
                escapeshellarg($inputPath),
                $quality,
                escapeshellarg($outputPath)
            );
            break;
    }
    
    return $command;
}

function createVideoFromFrames($framesDir, $videoOutputPath, $extension, $ffmpegPath) {
    $videoCommand = sprintf(
        '%s -framerate 1 -i %s/image_%%05d%s -vf "scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2" -c:v libx264 -preset veryslow -crf 30 -pix_fmt yuv420p -r 30 -y %s 2>/dev/null',
        escapeshellcmd($ffmpegPath),
        escapeshellarg($framesDir),
        $extension,
        escapeshellarg($videoOutputPath)
    );
    
    error_log("Executing video command: " . $videoCommand);
    exec($videoCommand, $videoOutput, $videoReturnCode);
    
    return $videoReturnCode === 0;
}

function createVideoFromAVIF($avifFramesDir, $videoOutputPath, $ffmpegPath) {
    // Create temporary directory for JPEG conversion
    $tempJpegDir = dirname($videoOutputPath) . '/temp_jpeg_' . uniqid();
    if (!file_exists($tempJpegDir)) {
        mkdir($tempJpegDir, 0755, true);
    }
    
    try {
        // Convert AVIF frames to JPEG first
        $avifFiles = glob($avifFramesDir . '/image_*.avif');
        sort($avifFiles);
        
        error_log("Converting " . count($avifFiles) . " AVIF frames to JPEG for video creation...");
        
        foreach ($avifFiles as $index => $avifFile) {
            $frameNumber = $index + 1;
            $jpegFile = $tempJpegDir . '/image_' . sprintf('%05d', $frameNumber) . '.jpg';
            
            $convertCommand = sprintf(
                '%s -y -i %s -c:v mjpeg -q:v 5 %s 2>/dev/null',
                escapeshellcmd($ffmpegPath),
                escapeshellarg($avifFile),
                escapeshellarg($jpegFile)
            );
            
            exec($convertCommand, $output, $returnCode);
            if ($returnCode !== 0) {
                error_log("Failed to convert AVIF frame: " . $avifFile);
                return false;
            }
        }
        
        // Create video from converted JPEG frames
        $success = createVideoFromFrames($tempJpegDir, $videoOutputPath, '.jpg', $ffmpegPath);
        
        return $success;
        
    } finally {
        // Clean up temporary JPEG files
        if (file_exists($tempJpegDir)) {
            exec('rm -rf ' . escapeshellarg($tempJpegDir));
        }
    }
} 