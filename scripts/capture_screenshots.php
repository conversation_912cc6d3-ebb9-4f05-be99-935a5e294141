<?php

/**
 * Screenshot Capture Script (Updated for Async Processing)
 *
 * This script has been updated to use the new asynchronous job processing system.
 * It maintains backward compatibility while providing improved scalability.
 *
 * For the full async implementation, use: capture_screenshots_async.php
 */

// Enable output buffering
ob_start();

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Log the start of the script
error_log("Starting capture_screenshots.php script (async version)");
echo "Starting capture_screenshots.php script (async version)\n";

use App\AsyncJobs\AsyncJobManager;

try {
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader file not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;
    error_log("Autoloader loaded successfully");
    echo "Autoloader loaded successfully\n";

    $configPath = __DIR__ . '/../config.php';
    if (!file_exists($configPath)) {
        throw new Exception("Config file not found at: " . $configPath);
    }
    $config = require $configPath;
    error_log("Config loaded successfully");
    echo "Config loaded successfully\n";

    // Check if async jobs are configured
    if (isset($config['async_jobs']) && $config['async_jobs']['screenshot']['enabled']) {
        // Use new async system
        error_log("Using async job processing system");
        echo "Using async job processing system\n";

        $jobManager = new AsyncJobManager($config);
        error_log("AsyncJobManager initialized");
        echo "AsyncJobManager initialized\n";

        // Create screenshot jobs for all cameras
        error_log("Creating screenshot jobs");
        echo "Creating screenshot jobs\n";
        $createdJobs = $jobManager->createScreenshotJobs();
        error_log("Created " . count($createdJobs) . " screenshot jobs");
        echo "Created " . count($createdJobs) . " screenshot jobs\n";

        // Process the jobs
        error_log("Processing screenshot jobs");
        echo "Processing screenshot jobs\n";
        $results = $jobManager->processJobs('screenshot');

        $successful = array_filter($results, fn($r) => $r['success']);
        $failed = array_filter($results, fn($r) => !$r['success']);

        error_log("Processing completed - Success: " . count($successful) . ", Failed: " . count($failed));
        echo "Processing completed - Success: " . count($successful) . ", Failed: " . count($failed) . "\n";

        // Cleanup
        $jobManager->cleanup();
        error_log("Cleanup completed");
        echo "Cleanup completed\n";

    } else {
        // Fall back to legacy synchronous system
        error_log("Async jobs not configured, using legacy synchronous system");
        echo "Async jobs not configured, using legacy synchronous system\n";

        if (!isset($config['screenshot'])) {
            throw new Exception("Screenshot configuration is missing");
        }

        if (!isset($config['screenshot']['ffmpeg_path'])) {
            throw new Exception("FFmpeg path is not configured");
        }

        if (!file_exists($config['screenshot']['ffmpeg_path'])) {
            throw new Exception("FFmpeg not found at: " . $config['screenshot']['ffmpeg_path']);
        }

        error_log("Initializing legacy ScreenshotCapture");
        echo "Initializing legacy ScreenshotCapture\n";
        $capture = new \App\ScreenshotCapture($config);
        error_log("ScreenshotCapture initialized");
        echo "ScreenshotCapture initialized\n";

        error_log("Starting capture of all cameras");
        echo "Starting capture of all cameras\n";
        $capture->captureAllCameras();
    }

    error_log("Capture completed successfully");
    echo "Capture completed successfully\n";

} catch (Exception $e) {
    error_log("Error in capture_screenshots.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
    http_response_code(500);
}

// Flush the output buffer
ob_end_flush();