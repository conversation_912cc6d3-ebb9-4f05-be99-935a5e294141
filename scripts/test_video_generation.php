#!/usr/bin/env php
<?php

/**
 * Video Generation Test Script
 * 
 * Tests the video timelapse generation system using only 10 frames.
 * Creates sample timelapses for all three periods (1d, 7d, 30d).
 * 
 * Usage: php scripts/test_video_generation.php [options]
 */

// Set memory and execution limits for video processing
ini_set('memory_limit', '2G');
set_time_limit(1800); // 30 minutes max for testing

// Import required classes
use App\AsyncJobs\VideoJobWorker;

echo "🎬 ========================================\n";
echo "   VIDEO GENERATION TEST SCRIPT\n";
echo "========================================\n\n";

try {
    $startTime = microtime(true);
    
    // Parse command line arguments
    $options = getopt('h', ['help', 'camera:', 'period:', 'frames:']);
    
    if (isset($options['h']) || isset($options['help'])) {
        showHelp();
        exit(0);
    }
    
    // Load autoloader
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader file not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;
    echo "✅ Autoloader loaded successfully\n";

    // Load configuration
    $configPath = __DIR__ . '/../config.php';
    if (!file_exists($configPath)) {
        throw new Exception("Config file not found at: " . $configPath);
    }
    $config = require $configPath;
    echo "✅ Configuration loaded successfully\n";

    // Validate video configuration
    if (!($config['async_jobs']['video']['enabled'] ?? false)) {
        throw new Exception("❌ Video generation is disabled in configuration");
    }
    
    echo "✅ Video configuration validated\n";

    // Test parameters
    $testCameraId = isset($options['camera']) ? (int)$options['camera'] : 1;
    $testPeriod = $options['period'] ?? '1d';
    $maxFrames = isset($options['frames']) ? (int)$options['frames'] : 10;
    
    echo "🧪 Test Parameters:\n";
    echo "   Camera ID: {$testCameraId}\n";
    echo "   Period: {$testPeriod}\n";
    echo "   Max Frames: {$maxFrames}\n\n";

    // Validate period
    if (!isset($config['async_jobs']['video']['periods'][$testPeriod])) {
        throw new Exception("❌ Invalid period: {$testPeriod}. Available: " . implode(', ', array_keys($config['async_jobs']['video']['periods'])));
    }

    // Create test screenshots directory if needed
    $screenshotsDir = $config['async_jobs']['video']['screenshots_dir'];
    $testCameraDir = $screenshotsDir . '/camera_' . $testCameraId;
    
    if (!is_dir($testCameraDir)) {
        echo "📁 Creating test screenshots directory: {$testCameraDir}\n";
        if (!mkdir($testCameraDir, 0755, true)) {
            throw new Exception("Failed to create test directory: {$testCameraDir}");
        }
    }

    // Check for existing screenshots
    $existingScreenshots = glob($testCameraDir . '/camera_' . $testCameraId . '_*.jpg');
    echo "📸 Found " . count($existingScreenshots) . " existing screenshots\n";

    if (count($existingScreenshots) < $maxFrames) {
        echo "⚠️  Not enough screenshots for test. Need at least {$maxFrames} frames.\n";
        echo "   Creating dummy test screenshots...\n";
        
        $dummyScreenshots = createDummyScreenshots($testCameraDir, $testCameraId, $maxFrames);
        echo "✅ Created " . count($dummyScreenshots) . " dummy screenshots\n";
    }

    // Create test job data (without database dependency)
    $jobData = [
        'job' => [
            'id' => 999999, // Test job ID
            'camera_id' => $testCameraId,
            'job_type' => 'video',
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s'),
            'config' => json_encode(['period' => $testPeriod])
        ],
        'config' => $config
    ];

    echo "\n🚀 Starting video generation test...\n";

    // Test direct video generation without database
    $result = testVideoGenerationDirect($testCameraId, $testPeriod, $config, $maxFrames);
    
    echo "\n📊 TEST RESULTS:\n";
    echo "   Camera ID: {$result['camera_id']}\n";
    echo "   Camera Name: {$result['camera_name']}\n";
    echo "   Period: {$result['period']}\n";
    echo "   Video Filename: {$result['video_filename']}\n";
    echo "   Video Path: {$result['video_path']}\n";
    echo "   Source Images: {$result['source_images_count']}\n";
    echo "   Selected Frames: {$result['selected_frames_count']}\n";
    echo "   Video Duration: {$result['video_duration']} seconds\n";
    echo "   File Size: " . round($result['file_size'] / 1024 / 1024, 2) . " MB\n";

    // Verify video file exists
    if (file_exists($result['video_path'])) {
        echo "\n✅ Video file created successfully!\n";
        echo "   Path: {$result['video_path']}\n";
        
        // Get video info using ffprobe if available
        $ffprobePath = '/usr/bin/ffprobe';
        if (file_exists($ffprobePath)) {
            $command = escapeshellcmd($ffprobePath) . ' -v quiet -print_format json -show_format -show_streams ' . escapeshellarg($result['video_path']);
            $output = shell_exec($command);
            
            if ($output) {
                $videoInfo = json_decode($output, true);
                if (isset($videoInfo['format']['duration'])) {
                    echo "   Actual Duration: " . round($videoInfo['format']['duration'], 2) . " seconds\n";
                }
                if (isset($videoInfo['streams'][0]['width'], $videoInfo['streams'][0]['height'])) {
                    echo "   Resolution: {$videoInfo['streams'][0]['width']}x{$videoInfo['streams'][0]['height']}\n";
                }
            }
        }
    } else {
        throw new Exception("❌ Video file was not created: {$result['video_path']}");
    }

    $totalTime = microtime(true) - $startTime;
    echo "\n⏱️  Total execution time: " . round($totalTime, 2) . " seconds\n";
    echo "🎉 Video generation test completed successfully!\n";

} catch (Exception $e) {
    echo "\n❌ ERROR: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . " (Line: " . $e->getLine() . ")\n";
    
    if (isset($config['debug']) && $config['debug']) {
        echo "\n🔍 Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
    exit(1);
}

/**
 * Create dummy screenshots for testing
 */
function createDummyScreenshots(string $cameraDir, int $cameraId, int $count): array {
    $created = [];
    $baseTimestamp = time() - (3600 * 24); // Start 24 hours ago
    
    for ($i = 0; $i < $count; $i++) {
        $timestamp = $baseTimestamp + ($i * 3600); // One per hour
        $filename = "camera_{$cameraId}_{$timestamp}.jpg";
        $filepath = $cameraDir . '/' . $filename;
        
        // Create a simple colored rectangle as dummy image
        $image = imagecreate(640, 480);
        $bgColor = imagecolorallocate($image, rand(100, 255), rand(100, 255), rand(100, 255));
        $textColor = imagecolorallocate($image, 0, 0, 0);
        
        imagestring($image, 5, 250, 220, "Frame " . ($i + 1), $textColor);
        imagestring($image, 3, 200, 250, date('Y-m-d H:i:s', $timestamp), $textColor);
        
        imagejpeg($image, $filepath, 75);
        imagedestroy($image);
        
        $created[] = $filepath;
    }
    
    return $created;
}

/**
 * Test video generation directly without database dependency
 */
function testVideoGenerationDirect(int $cameraId, string $period, array $config, int $maxFrames): array {
    // Simulate camera data
    $camera = [
        'id' => $cameraId,
        'name' => 'TEST_CAMERA_' . $cameraId,
        'hls_url' => 'https://test.example.com/camera' . $cameraId . '/index.m3u8'
    ];

    // Get period configuration
    $periodConfig = $config['async_jobs']['video']['periods'][$period];

    // Collect screenshots
    $screenshotsDir = $config['async_jobs']['video']['screenshots_dir'];
    $cameraDir = $screenshotsDir . '/camera_' . $cameraId;

    $screenshots = [];
    if (is_dir($cameraDir)) {
        $files = glob($cameraDir . '/camera_' . $cameraId . '_*.jpg');
        foreach ($files as $file) {
            if (preg_match('/camera_\d+_(\d+)\.jpg$/', basename($file), $matches)) {
                $screenshots[] = [
                    'filename' => basename($file),
                    'path' => $file,
                    'timestamp' => (int)$matches[1],
                    'size' => filesize($file)
                ];
            }
        }
    }

    // Sort by timestamp
    usort($screenshots, function($a, $b) {
        return $a['timestamp'] <=> $b['timestamp'];
    });

    // Limit to maxFrames for testing
    $screenshots = array_slice($screenshots, 0, $maxFrames);

    if (empty($screenshots)) {
        throw new Exception("No screenshots found for testing");
    }

    echo "   Found " . count($screenshots) . " screenshots for testing\n";

    // Apply frame sampling
    $frameSkip = $periodConfig['frame_skip'];
    if ($frameSkip <= 1) {
        $selectedFrames = $screenshots;
    } else {
        $selected = [];
        for ($i = 0; $i < count($screenshots); $i += $frameSkip) {
            $selected[] = $screenshots[$i];
        }
        $selectedFrames = $selected;
    }

    echo "   Selected " . count($selectedFrames) . " frames after sampling\n";

    // Generate video filename
    $timestamp = time();
    $firstFrameTimestamp = $selectedFrames[0]['timestamp'];
    $lastFrameTimestamp = end($selectedFrames)['timestamp'];

    $videoFilename = sprintf(
        'camera_%d_%s_%d_%d.mp4',
        $cameraId,
        $period,
        $firstFrameTimestamp,
        $lastFrameTimestamp
    );

    $outputDir = $config['async_jobs']['video']['output_dir'];
    $tempDir = $config['async_jobs']['video']['temp_dir'];
    $outputPath = $outputDir . '/' . $videoFilename;

    // Create directories
    if (!is_dir($outputDir)) mkdir($outputDir, 0755, true);
    if (!is_dir($tempDir)) mkdir($tempDir, 0755, true);

    // Create symlinks for FFmpeg
    $tempInputDir = $tempDir . '/input_test_' . $timestamp;
    if (!is_dir($tempInputDir)) mkdir($tempInputDir, 0755, true);

    $frameNumber = 0;
    foreach ($selectedFrames as $frame) {
        $symlinkPath = sprintf('%s/frame_%06d.jpg', $tempInputDir, $frameNumber);
        symlink($frame['path'], $symlinkPath);
        $frameNumber++;
    }

    $inputPattern = $tempInputDir . '/frame_%06d.jpg';

    echo "   Created " . $frameNumber . " symlinks for FFmpeg\n";

    // Generate video using FFmpeg
    $commandTemplate = $config['async_jobs']['video']['command_template'];
    $ffmpegPath = $config['async_jobs']['video']['ffmpeg_path'];
    $framerate = $periodConfig['framerate'];
    $duration = $periodConfig['video_duration'];
    $crf = $periodConfig['crf_quality'];

    $command = sprintf(
        $commandTemplate,
        escapeshellcmd($ffmpegPath),
        $framerate,
        escapeshellarg($inputPattern),
        $duration,
        $crf,
        escapeshellarg($outputPath)
    );

    echo "   Executing FFmpeg command...\n";
    echo "   Command: " . $command . "\n";

    $startTime = microtime(true);
    $output = [];
    $returnCode = 0;
    exec($command . ' 2>&1', $output, $returnCode);
    $executionTime = microtime(true) - $startTime;

    // Clean up symlinks
    $files = glob($tempInputDir . '/*');
    foreach ($files as $file) {
        if (is_link($file)) unlink($file);
    }
    rmdir($tempInputDir);

    if ($returnCode !== 0) {
        throw new Exception("FFmpeg failed with return code {$returnCode}: " . implode("\n", $output));
    }

    if (!file_exists($outputPath)) {
        throw new Exception("Video file was not created: {$outputPath}");
    }

    return [
        'camera_id' => $cameraId,
        'camera_name' => $camera['name'],
        'period' => $period,
        'timestamp' => $timestamp,
        'video_filename' => $videoFilename,
        'video_path' => $outputPath,
        'source_images_count' => count($screenshots),
        'selected_frames_count' => count($selectedFrames),
        'video_duration' => $duration,
        'file_size' => filesize($outputPath),
        'execution_time' => $executionTime,
        'ffmpeg_output' => implode("\n", $output)
    ];
}

/**
 * Show help information
 */
function showHelp(): void {
    echo "Video Generation Test Script\n\n";
    echo "USAGE:\n";
    echo "  php scripts/test_video_generation.php [options]\n\n";
    echo "OPTIONS:\n";
    echo "  --camera ID         Test with specific camera ID (default: 1)\n";
    echo "  --period PERIOD     Test specific period: 1d, 7d, or 30d (default: 1d)\n";
    echo "  --frames COUNT      Maximum frames to use for test (default: 10)\n";
    echo "  -h, --help          Show this help message\n\n";
    echo "EXAMPLES:\n";
    echo "  php scripts/test_video_generation.php\n";
    echo "  php scripts/test_video_generation.php --camera 2 --period 7d\n";
    echo "  php scripts/test_video_generation.php --frames 20\n\n";
}
