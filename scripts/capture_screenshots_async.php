#!/usr/bin/env php
<?php

/**
 * Asynchronous Screenshot Capture Script
 * 
 * This script replaces the synchronous capture_screenshots.php with an async
 * implementation that can scale to 300+ cameras efficiently.
 * 
 * Features:
 * - Parallel processing using worker processes
 * - Optimized ffmpeg + cjpeg command pipeline
 * - Comprehensive logging and error handling
 * - Database job tracking
 * - Configurable worker limits and timeouts
 * - Safety: Backblaze uploads disabled by default
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set execution time limit
set_time_limit(7200); // 2 hours max

// Import required classes
use App\AsyncJobs\AsyncJobManager;

echo "🚀 ========================================\n";
echo "   ASYNCHRONOUS SCREENSHOT CAPTURE\n";
echo "========================================\n\n";

try {
    $startTime = microtime(true);

    // Load autoloader
    $autoloaderPath = __DIR__ . '/../vendor/autoload.php';
    if (!file_exists($autoloaderPath)) {
        throw new Exception("Autoloader file not found at: " . $autoloaderPath);
    }
    require $autoloaderPath;
    echo "✅ Autoloader loaded successfully\n";

    // Load configuration
    $configPath = __DIR__ . '/../config.php';
    if (!file_exists($configPath)) {
        throw new Exception("Config file not found at: " . $configPath);
    }
    $config = require $configPath;
    echo "✅ Configuration loaded successfully\n";

    // Check if async jobs are configured
    if (!isset($config['async_jobs'])) {
        throw new Exception("Async jobs configuration is missing. Please update your config.php with async_jobs settings.");
    }

    if (!$config['async_jobs']['screenshot']['enabled']) {
        throw new Exception("Screenshot jobs are disabled in configuration");
    }

    echo "✅ Async jobs configuration validated\n";

    // Initialize job manager
    echo "🔧 Initializing async job manager...\n";
    $jobManager = new AsyncJobManager($config);
    echo "✅ Job manager initialized successfully\n";

    // Display configuration summary
    $jobTypes = $jobManager->getJobTypes();
    echo "\n📋 Job Types Configuration:\n";
    foreach ($jobTypes as $type => $typeConfig) {
        $status = $typeConfig['enabled'] ? '✅ Enabled' : '❌ Disabled';
        echo "   {$type}: {$status} - {$typeConfig['description']}\n";
    }

    // Check current queue status
    echo "\n📊 Current Queue Status:\n";
    $queueStatus = $jobManager->getQueueStatus();
    foreach ($queueStatus as $jobType => $stats) {
        if ($stats['enabled']) {
            echo "   {$jobType}: Pending({$stats['pending']}) Running({$stats['running']}) Completed({$stats['completed']}) Failed({$stats['failed']})\n";
        }
    }

    // Parse command line arguments
    $cameraIds = null;
    $forceCreate = false;
    $cleanupOnly = false;
    
    if ($argc > 1) {
        for ($i = 1; $i < $argc; $i++) {
            $arg = $argv[$i];
            
            if ($arg === '--cameras' && isset($argv[$i + 1])) {
                $cameraIds = array_map('intval', explode(',', $argv[$i + 1]));
                $i++; // Skip next argument
                echo "🎯 Processing specific cameras: " . implode(', ', $cameraIds) . "\n";
            } elseif ($arg === '--force') {
                $forceCreate = true;
                echo "⚠️  Force mode: Will create jobs even if active jobs exist\n";
            } elseif ($arg === '--cleanup-only') {
                $cleanupOnly = true;
                echo "🧹 Cleanup mode: Only performing maintenance tasks\n";
            } elseif ($arg === '--help' || $arg === '-h') {
                showHelp();
                exit(0);
            }
        }
    }

    if (!$cleanupOnly) {
        // Create screenshot jobs
        echo "\n🎬 Creating screenshot jobs...\n";
        $createdJobs = $jobManager->createScreenshotJobs($cameraIds);
        
        if (empty($createdJobs)) {
            echo "ℹ️  No new jobs created (cameras may already have active jobs)\n";
        } else {
            echo "✅ Created " . count($createdJobs) . " screenshot jobs\n";
            foreach ($createdJobs as $job) {
                echo "   Job {$job['job_id']}: Camera {$job['camera_id']} ({$job['camera_name']})\n";
            }
        }

        // Process screenshot jobs
        echo "\n⚡ Processing screenshot jobs...\n";
        $results = $jobManager->processJobs('screenshot');
        
        // Display results summary
        $successful = array_filter($results, fn($r) => $r['success']);
        $failed = array_filter($results, fn($r) => !$r['success']);
        
        echo "\n📈 Processing Results:\n";
        echo "   Total jobs processed: " . count($results) . "\n";
        echo "   Successful: " . count($successful) . "\n";
        echo "   Failed: " . count($failed) . "\n";
        
        if (!empty($failed)) {
            echo "\n❌ Failed Jobs:\n";
            foreach ($failed as $failedJob) {
                echo "   Camera {$failedJob['camera_id']}: {$failedJob['error']}\n";
            }
        }
        
        if (!empty($successful)) {
            $executionTimes = array_column($successful, 'execution_time');
            $avgTime = array_sum($executionTimes) / count($executionTimes);
            $maxTime = max($executionTimes);
            
            echo "\n⏱️  Performance Metrics:\n";
            echo "   Average execution time: " . round($avgTime, 2) . "s\n";
            echo "   Maximum execution time: " . round($maxTime, 2) . "s\n";
        }
    }

    // Perform cleanup
    echo "\n🧹 Performing cleanup...\n";
    $cleanupResults = $jobManager->cleanup();
    echo "   Cleaned up {$cleanupResults['cleaned_jobs']} old jobs\n";
    echo "   Cleaned up {$cleanupResults['cleaned_logs']} old log entries\n";
    echo "   Handled {$cleanupResults['timed_out_jobs']} timed out jobs\n";

    // Final queue status
    echo "\n📊 Final Queue Status:\n";
    $finalStatus = $jobManager->getQueueStatus();
    foreach ($finalStatus as $jobType => $stats) {
        if ($stats['enabled']) {
            echo "   {$jobType}: Pending({$stats['pending']}) Running({$stats['running']}) Completed({$stats['completed']}) Failed({$stats['failed']})\n";
        }
    }

    $totalTime = microtime(true) - $startTime;
    echo "\n🏁 Async screenshot capture completed successfully!\n";
    echo "⏱️  Total execution time: " . round($totalTime, 2) . " seconds\n\n";

} catch (Exception $e) {
    echo "\n❌ Error in async screenshot capture: " . $e->getMessage() . "\n";
    echo "📍 File: " . $e->getFile() . "\n";
    echo "📍 Line: " . $e->getLine() . "\n";
    echo "📍 Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

function showHelp() {
    echo "Asynchronous Screenshot Capture\n\n";
    echo "USAGE:\n";
    echo "  php capture_screenshots_async.php [options]\n\n";
    echo "OPTIONS:\n";
    echo "  --cameras ID1,ID2,ID3   Process only specific camera IDs\n";
    echo "  --force                 Create jobs even if active jobs exist\n";
    echo "  --cleanup-only          Only perform cleanup, don't process screenshots\n";
    echo "  -h, --help              Show this help message\n\n";
    echo "EXAMPLES:\n";
    echo "  php capture_screenshots_async.php\n";
    echo "  php capture_screenshots_async.php --cameras 1,2,3\n";
    echo "  php capture_screenshots_async.php --cleanup-only\n";
    echo "  php capture_screenshots_async.php --force\n\n";
    echo "DESCRIPTION:\n";
    echo "  This script creates and processes screenshot capture jobs asynchronously.\n";
    echo "  It uses a process pool to handle multiple cameras in parallel, making it\n";
    echo "  suitable for scaling to 300+ cameras.\n\n";
    echo "  Safety features:\n";
    echo "  - Backblaze uploads are disabled by default\n";
    echo "  - Comprehensive logging and error handling\n";
    echo "  - Job retry mechanism for failed captures\n";
    echo "  - Automatic cleanup of old jobs and logs\n\n";
}
