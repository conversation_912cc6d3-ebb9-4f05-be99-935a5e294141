<?php
// info.php - Minimal system info for screenshot/video scripts

$config = require __DIR__ . '/../../config.php';

if( !isset($_GET['key']) || $_GET['key'] !== $config['security_key'] ) {
    echo "Access denied";
    exit;
}

function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, $precision) . ' ' . $units[$i];
}

function getDirSize($dir) {
    $size = 0;
    if (!is_dir($dir)) return 0;
    foreach (new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir, FilesystemIterator::SKIP_DOTS)) as $file) {
        $size += $file->getSize();
    }
    return $size;
}

function formatMB($bytes, $precision = 2) {
    return round($bytes / 1024 / 1024, $precision) . ' MB';
}

function getDirectoryTreeWithSizes($baseDir, $level = 1, $maxLevel = 3) {
    $result = [];
    if (!is_dir($baseDir)) return $result;
    $items = scandir($baseDir);
    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;
        $path = $baseDir . DIRECTORY_SEPARATOR . $item;
        if (is_dir($path)) {
            $size = getDirSize($path);
            $entry = [
                'name' => $item,
                'size' => $size,
                'children' => ($level < $maxLevel) ? getDirectoryTreeWithSizes($path, $level + 1, $maxLevel) : []
            ];
            $result[] = $entry;
        } else {
            // Optionally skip files at top level
        }
    }
    return $result;
}

function renderDirectoryTree($tree, $prefix = '') {
    $html = '<ul>';
    foreach ($tree as $entry) {
        $html .= '<li>' . htmlspecialchars($entry['name']) .
            ' <span style="color:#888">(' . formatMB($entry['size']) . ')</span>';
        if (!empty($entry['children'])) {
            $html .= renderDirectoryTree($entry['children']);
        }
        $html .= '</li>';
    }
    $html .= '</ul>';
    return $html;
}

// System info: RAM, CPU, load
function getSystemMemoryInfo() {
    $mem = [
        'total' => null,
        'free' => null,
        'used' => null
    ];
    if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        // Windows: not implemented
        return $mem;
    }
    if (is_readable('/proc/meminfo')) {
        $data = file_get_contents('/proc/meminfo');
        if (preg_match('/MemTotal:\s+(\d+)/', $data, $m)) {
            $mem['total'] = (int)$m[1] * 1024;
        }
        if (preg_match('/MemAvailable:\s+(\d+)/', $data, $m)) {
            $mem['free'] = (int)$m[1] * 1024;
        } elseif (preg_match('/MemFree:\s+(\d+)/', $data, $m)) {
            $mem['free'] = (int)$m[1] * 1024;
        }
        if ($mem['total'] !== null && $mem['free'] !== null) {
            $mem['used'] = $mem['total'] - $mem['free'];
        }
    } else {
        // Fallback: use shell_exec (macOS, BSD)
        $total = (int)trim(shell_exec("sysctl -n hw.memsize 2>/dev/null"));
        $free = (int)trim(shell_exec("vm_stat | grep 'free' | awk '{print $3}' | sed 's/\.//' 2>/dev/null"));
        if ($total > 0) $mem['total'] = $total;
        if ($free > 0 && $mem['total']) {
            $pageSize = (int)trim(shell_exec("vm_stat | grep 'page size of' | awk '{print $8}' 2>/dev/null"));
            if ($pageSize > 0) $mem['free'] = $free * $pageSize;
        }
        if ($mem['total'] !== null && $mem['free'] !== null) {
            $mem['used'] = $mem['total'] - $mem['free'];
        }
    }
    return $mem;
}

function getCpuCoreCount() {
    $count = (int)trim(shell_exec('nproc 2>/dev/null'));
    if ($count > 0) return $count;
    $count = (int)trim(shell_exec('sysctl -n hw.ncpu 2>/dev/null'));
    if ($count > 0) return $count;
    return 1;
}

function getLoadAverage() {
    $load = sys_getloadavg();
    return $load ? $load : [null, null, null];
}

$requiredExtensions = [
    'gd', 'imagick', 'pdo', 'pdo_mysql', 'curl', 'json', 'mbstring', 'fileinfo', 'openssl', 'exif', 'zlib',
    'bcmath', 'sockets', 'tokenizer', 'dom', 'simplexml', 'xml', 'ctype', 'filter', 'hash', 'session',
    'libxml', 'pcntl', 'posix', 'SPL', 'standard'
];
$loadedExtensions = get_loaded_extensions();
$missingExtensions = array_diff($requiredExtensions, $loadedExtensions);

$phpInfo = [
    'PHP version' => phpversion(),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size'),
];

$osInfo = php_uname();

$siteRoot = realpath(__DIR__ . '/../../..');
$timelapsesDir = $siteRoot . '/html/timelapses';
$screenshotsDir = $siteRoot . '/html/screenshots';

$siteSize = getDirSize($siteRoot);
$timelapsesSize = getDirSize($timelapsesDir);
$screenshotsSize = getDirSize($screenshotsDir);

$diskFree = disk_free_space($siteRoot);
$diskTotal = disk_total_space($siteRoot);

$directoryTree = getDirectoryTreeWithSizes($siteRoot, 1, 3);
$memInfo = getSystemMemoryInfo();
$cpuCores = getCpuCoreCount();
$loadAvg = getLoadAverage();

?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>System Info for Screenshot/Video Scripts</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f5f5f5; margin: 0; padding: 0; }
        .container { max-width: 700px; margin: 40px auto; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 30px; }
        h1 { text-align: center; color: #333; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #444; border-bottom: 1px solid #ddd; padding-bottom: 5px; margin-bottom: 15px; }
        .info-table { width: 100%; border-collapse: collapse; }
        .info-table td { padding: 6px 10px; border-bottom: 1px solid #eee; }
        .info-table td.label { font-weight: bold; color: #222; width: 220px; }
        .warn { background: #fff3cd; color: #856404; border: 1px solid #ffeeba; padding: 10px 15px; border-radius: 5px; margin-bottom: 20px; }
        .ok { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; padding: 10px 15px; border-radius: 5px; margin-bottom: 20px; }
        ul { margin: 0 0 0 20px; }
    </style>
</head>
<body>
<div class="container">
    <h1>System Info for Screenshot/Video Scripts</h1>

    <div class="section">
        <h2>PHP &amp; OS</h2>
        <table class="info-table">
            <tr><td class="label">PHP version</td><td><?= htmlspecialchars($phpInfo['PHP version']) ?></td></tr>
            <tr><td class="label">memory_limit</td><td><?= htmlspecialchars($phpInfo['memory_limit']) ?></td></tr>
            <tr><td class="label">max_execution_time</td><td><?= htmlspecialchars($phpInfo['max_execution_time']) ?></td></tr>
            <tr><td class="label">upload_max_filesize</td><td><?= htmlspecialchars($phpInfo['upload_max_filesize']) ?></td></tr>
            <tr><td class="label">post_max_size</td><td><?= htmlspecialchars($phpInfo['post_max_size']) ?></td></tr>
            <tr><td class="label">OS info</td><td><?= htmlspecialchars($osInfo) ?></td></tr>
        </table>
    </div>

    <div class="section">
        <h2>Disk &amp; Directory Sizes</h2>
        <table class="info-table">
            <tr><td class="label">Site root</td><td><?= htmlspecialchars($siteRoot) ?></td></tr>
            <tr><td class="label">Total site size</td><td><?= formatBytes($siteSize) ?></td></tr>
            <tr><td class="label">timelapses/ size</td><td><?= formatBytes($timelapsesSize) ?></td></tr>
            <tr><td class="label">screenshots/ size</td><td><?= formatBytes($screenshotsSize) ?></td></tr>
            <tr><td class="label">Disk free</td><td><?= formatBytes($diskFree) ?></td></tr>
            <tr><td class="label">Disk total</td><td><?= formatBytes($diskTotal) ?></td></tr>
        </table>
    </div>

    <div class="section">
        <h2>Site Directory Structure (Top 2 Levels, with Size)</h2>
        <?php echo renderDirectoryTree($directoryTree); ?>
    </div>

    <div class="section">
        <h2>System Resources</h2>
        <table class="info-table">
            <tr><td class="label">Total RAM</td><td><?= $memInfo['total'] ? formatMB($memInfo['total']) : 'N/A' ?></td></tr>
            <tr><td class="label">Free RAM</td><td><?= $memInfo['free'] ? formatMB($memInfo['free']) : 'N/A' ?></td></tr>
            <tr><td class="label">Used RAM</td><td><?= $memInfo['used'] ? formatMB($memInfo['used']) : 'N/A' ?></td></tr>
            <tr><td class="label">CPU cores</td><td><?= htmlspecialchars($cpuCores) ?></td></tr>
            <tr><td class="label">CPU load average (1/5/15 min)</td><td><?= htmlspecialchars(implode(' / ', array_map(function($v){return $v!==null?round($v,2):'N/A';}, $loadAvg))) ?></td></tr>
        </table>
    </div>

    <div class="section">
        <h2>PHP Extensions</h2>
        <?php if ($missingExtensions): ?>
            <div class="warn">
                <b>Warning:</b> The following required PHP extensions are <b>missing</b>:<br>
                <ul>
                    <?php foreach ($missingExtensions as $ext): ?>
                        <li><?= htmlspecialchars($ext) ?></li>
                    <?php endforeach; ?>
                </ul>
                Scripts may not work correctly until all required extensions are installed.
            </div>
        <?php else: ?>
            <div class="ok">All required PHP extensions are installed.</div>
        <?php endif; ?>
        <div style="margin-top:10px;">
            <b>Loaded extensions (<?= count($loadedExtensions) ?>):</b><br>
            <?= implode(', ', array_map('htmlspecialchars', $loadedExtensions)) ?>
        </div>
    </div>
</div>
</body>
</html>