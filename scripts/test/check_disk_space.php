<?php

/**
 * Disk Space Checker
 * Проверка свободного места на диске для планирования тестов
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

function getDirectorySize($directory) {
    if (!is_dir($directory)) {
        return 0;
    }
    
    $size = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    
    return $size;
}

echo "<pre>";
echo "💾 === DISK SPACE ANALYZER ===\n";
echo "📅 " . date('Y-m-d H:i:s') . "\n\n";

// Get current directory info
$currentDir = __DIR__;
echo "📁 Current directory: {$currentDir}\n\n";

// === BASIC DISK INFO ===
echo "🔍 === DISK SPACE INFORMATION ===\n";

$freeBytes = disk_free_space($currentDir);
$totalBytes = disk_total_space($currentDir);
$usedBytes = $totalBytes - $freeBytes;

$freeGB = $freeBytes / (1024 * 1024 * 1024);
$totalGB = $totalBytes / (1024 * 1024 * 1024);
$usedGB = $usedBytes / (1024 * 1024 * 1024);

$usedPercent = ($usedBytes / $totalBytes) * 100;
$freePercent = ($freeBytes / $totalBytes) * 100;

printf("📊 Total space:     %s (%.2f GB)\n", formatBytes($totalBytes), $totalGB);
printf("📊 Used space:      %s (%.2f GB) - %.1f%%\n", formatBytes($usedBytes), $usedGB, $usedPercent);
printf("📊 Free space:      %s (%.2f GB) - %.1f%%\n", formatBytes($freeBytes), $freeGB, $freePercent);

echo "\n";

// === SPACE STATUS ===
echo "⚠️  === SPACE STATUS ===\n";

if ($freeGB > 10) {
    echo "✅ Excellent: " . number_format($freeGB, 1) . " GB free - plenty of space for large tests\n";
} elseif ($freeGB > 5) {
    echo "🟡 Good: " . number_format($freeGB, 1) . " GB free - sufficient for medium tests\n";
} elseif ($freeGB > 1) {
    echo "🟠 Caution: " . number_format($freeGB, 1) . " GB free - limited space, use small tests only\n";
} else {
    echo "🔴 Critical: " . number_format($freeGB, 1) . " GB free - insufficient space for video tests\n";
}

echo "\n";

// === PROJECT DIRECTORIES ANALYSIS ===
echo "📂 === PROJECT DIRECTORIES ANALYSIS ===\n";

$projectDirs = [
    'Test Results' => __DIR__ . '/process_pool_mp4',
    'Screenshots' => __DIR__ . '/screenshots', 
    'Input Frames' => __DIR__ . '/process_pool_mp4/input',
    'JPEG Tests' => __DIR__ . '/process_pool_jpeg',
    'AVIF Tests' => __DIR__ . '/process_pool_avif'
];

$totalProjectSize = 0;

foreach ($projectDirs as $name => $dir) {
    if (is_dir($dir)) {
        $size = getDirectorySize($dir);
        $totalProjectSize += $size;
        printf("📁 %-15s: %s\n", $name, formatBytes($size));
    } else {
        printf("📁 %-15s: Not found\n", $name);
    }
}

echo "📁 " . str_repeat('-', 30) . "\n";
printf("📁 %-15s: %s\n", "Total project", formatBytes($totalProjectSize));

echo "\n";

// === ESTIMATES FOR COMMON TESTS ===
echo "🎬 === VIDEO TEST ESTIMATES ===\n";

$frameConfigs = [
    'Quick test (30 frames)' => [
        'frames' => 30,
        'duration_sec' => 1,
        'estimated_mb' => 1.5
    ],
    'Short test (300 frames)' => [
        'frames' => 300, 
        'duration_sec' => 10,
        'estimated_mb' => 8
    ],
    'Full test (900 frames)' => [
        'frames' => 900,
        'duration_sec' => 30, 
        'estimated_mb' => 25
    ]
];

foreach ($frameConfigs as $testName => $config) {
    $estimatedBytes = $config['estimated_mb'] * 1024 * 1024;
    $canFit = floor($freeBytes / $estimatedBytes);
    
    printf("🎥 %-25s: ~%s per video (can fit ~%d videos)\n", 
           $testName, 
           formatBytes($estimatedBytes), 
           $canFit);
}

echo "\n";

// === CRF TEST ESTIMATES ===
echo "🧪 === CRF TEST ESTIMATES ===\n";

$crfTests = [
    'Single CRF (900 frames)' => 1,
    '5 CRF values (900 frames)' => 5,
    '10 CRF values (900 frames)' => 10
];

foreach ($crfTests as $testName => $videoCount) {
    $estimatedTotalMB = $videoCount * 25; // 25MB per 30-second video
    $estimatedBytes = $estimatedTotalMB * 1024 * 1024;
    $enoughSpace = $freeBytes > $estimatedBytes;
    
    $status = $enoughSpace ? "✅" : "❌";
    printf("%s %-30s: ~%s total\n", 
           $status,
           $testName, 
           formatBytes($estimatedBytes));
}

echo "\n";

// === CLEANUP SUGGESTIONS ===
echo "🧹 === CLEANUP SUGGESTIONS ===\n";

if ($freeGB < 5) {
    echo "🗑️  Consider cleaning up old test results:\n";
    
    // Check for old test directories
    $testDirs = [
        __DIR__ . '/process_pool_mp4/crf_extended_test_*',
        __DIR__ . '/process_pool_mp4/crf_test_*',
        __DIR__ . '/process_pool_mp4/results_*',
        __DIR__ . '/process_pool_jpeg/results_*',
        __DIR__ . '/screenshots/test_results_*'
    ];
    
    foreach ($testDirs as $pattern) {
        $dirs = glob($pattern);
        if (!empty($dirs)) {
            echo "   • Found " . count($dirs) . " directories matching: " . basename($pattern) . "\n";
        }
    }
    
    echo "\n   💡 You can safely delete old test result directories to free up space.\n";
} else {
    echo "✅ Disk space is sufficient. No immediate cleanup needed.\n";
}

echo "\n";

// === RECOMMENDATIONS ===
echo "💡 === RECOMMENDATIONS ===\n";

if ($freeGB > 10) {
    echo "🚀 You can run comprehensive tests with multiple CRF values\n";
    echo "📈 Consider testing with full 900-frame videos for accurate results\n";
} elseif ($freeGB > 5) {
    echo "📊 Run medium-scale tests with 300-600 frames\n";
    echo "🎯 Limit simultaneous CRF tests to 3-5 values\n";
} elseif ($freeGB > 1) {
    echo "⚡ Use quick tests with 30-100 frames only\n";
    echo "🔄 Clean up results between tests\n";
} else {
    echo "🚨 Free up disk space before running any video tests\n";
    echo "🗑️  Remove old test results and temporary files\n";
}

echo "\n=== END OF ANALYSIS ===\n";

?> 