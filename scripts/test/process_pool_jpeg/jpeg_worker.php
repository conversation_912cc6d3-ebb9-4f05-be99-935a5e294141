<?php

namespace App\Test;

use PDO;
use Exception;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Extend memory and time limits
ini_set('memory_limit', '512M');
set_time_limit(600); // 10 minutes for JPEG operations

class JpegWorker {
    private $config;
    private $testResultsDir;
    private $job;
    private $db;
    
    // JPEG formats (from main tester)
    private $jpegFormats = [
        // 'lossless' => [
        //     'name' => 'Lossless JPEG',
        //     'command_template' => '%s -i "%s" -vframes 1 -q:v 1 "%s"',
        //     'description' => 'Lossless JPEG with q:v 1',
        //     'extension' => 'jpg'
        // ],
        // 'high-5' => [
        //     'name' => 'High Quality 5',
        //     'command_template' => '%s -i "%s" -vframes 1 -q:v 5 "%s"',
        //     'description' => 'High quality JPEG with q:v 5',
        //     'extension' => 'jpg'
        // ],
        'mozjpeg_postprocess' => [
            'name' => 'MozJPEG Post-Process',
            'command_template' => '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s.tmp" && /usr/local/bin/cjpeg -quality 75 -progressive -optimize -nojfif "%s.tmp" > "%s" && rm "%s.tmp"',
            'description' => 'Basic JPEG -> MozJPEG post-processing (best compression)',
            'extension' => 'jpg'
        ],
    ];
    
    // HLS Segment management - NO CACHING for unique frames
    private $screenshotCount = 10;

    public function __construct($jobData) {
        $this->job = $jobData['job'];
        $this->testResultsDir = $jobData['test_results_dir'];
        $this->config = $jobData['config'];
        
        $this->initializeDatabase();
    }

    private function initializeDatabase() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                $this->config['database']['host'],
                $this->config['database']['dbname'],
                $this->config['database']['charset']
            );

            $this->db = new PDO(
                $dsn,
                $this->config['database']['username'],
                $this->config['database']['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        } catch (Exception $e) {
            error_log("JPEG Worker: Database connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    public function execute() {
        try {
            $startTime = microtime(true);
            
            $result = $this->processJpegFormat();
            
            $result['total_time'] = microtime(true) - $startTime;
            $result['worker_id'] = getmypid();
            
            // Output result as JSON for parent process to read
            echo "RESULT_JSON_START\n";
            echo json_encode($result, JSON_PRETTY_PRINT);
            echo "\nRESULT_JSON_END\n";
            
            error_log("JPEG Worker " . getmypid() . " completed format " . $this->job['format_key'] . " task");
            
        } catch (Exception $e) {
            $errorResult = [
                'format_key' => $this->job['format_key'],
                'format_name' => $this->job['format_name'] ?? 'Unknown',
                'success_count' => 0,
                'error_count' => 1,
                'error' => $e->getMessage(),
                'worker_id' => getmypid()
            ];
            
            echo "RESULT_JSON_START\n";
            echo json_encode($errorResult, JSON_PRETTY_PRINT);
            echo "\nRESULT_JSON_END\n";
            
            error_log("JPEG Worker " . getmypid() . " error: " . $e->getMessage());
        }
    }

    private function processJpegFormat() {
        $formatKey = $this->job['format_key'];
        $framesPerWorker = $this->job['frames_per_worker'];
        $format = $this->jpegFormats[$formatKey];
        
        $outputDir = $this->testResultsDir . "/process_pool/{$formatKey}";
        
        echo "JPEG Worker " . getmypid() . ": Processing {$framesPerWorker} frames for format {$format['name']}\n";
        
        // Get camera 1 data
        $camera = $this->getCamera1();
        if (!$camera) {
            throw new Exception("Camera 1 not found");
        }
        
        $successCount = 0;
        $errorCount = 0;
        $totalSize = 0;
        $files = [];
        $usedSegments = []; // Track used segments to avoid duplicates within worker

        for ($i = 1; $i <= $framesPerWorker; $i++) {
            $outputFile = sprintf("%s/frame_%03d_%s.%s", $outputDir, $i, $formatKey, $format['extension']);
            
            try {
                $result = $this->captureUsingHlsDirectWithUniqueness($camera['hls_url'], $outputFile, $format, $usedSegments);
                
                if ($result['success']) {
                    $fileSize = filesize($outputFile);
                    $totalSize += $fileSize;
                    $successCount++;
                    $files[] = basename($outputFile);
                    
                    echo "Worker " . getmypid() . ": Frame {$i}/{$framesPerWorker} completed ({$format['name']})\n";
                } else {
                    $errorCount++;
                    error_log("Worker " . getmypid() . ": Frame {$i} failed: " . ($result['error'] ?? 'Unknown error'));
                }

            } catch (Exception $e) {
                $errorCount++;
                error_log("Worker " . getmypid() . ": Frame {$i} exception: " . $e->getMessage());
            }
        }

        echo "Worker " . getmypid() . ": Format {$format['name']} processing completed. Success: {$successCount}, Errors: {$errorCount}\n";

        return [
            'format_key' => $formatKey,
            'format_name' => $format['name'],
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'total_size' => $totalSize,
            'files' => $files
        ];
    }

    // HLS Direct methods (same as in AVIF worker)
    private function captureUsingDirectSegment($segmentUrl, $outputFile, $format) {
        try {
            // Step 1: Download partial segment using Range requests (500KB)
            $tempPartialSegment = sys_get_temp_dir() . '/hls_partial_' . uniqid() . '.ts';
            $downloadResult = $this->downloadPartialSegment($segmentUrl, $tempPartialSegment);
            
            if (!$downloadResult['success']) {
                // Fallback: try without range if server doesn't support it
                $downloadResult = $this->downloadSegment($segmentUrl, $tempPartialSegment);
            }
            
            if (!$downloadResult['success']) {
                return [
                    'success' => false,
                    'error' => $downloadResult['error']
                ];
            }
            
            // Step 2: Extract I-frame and convert to JPEG with specified quality
            $extractResult = $this->extractIFrameToJpeg($tempPartialSegment, $outputFile, $format);
            
            // Cleanup
            if (file_exists($tempPartialSegment)) {
                unlink($tempPartialSegment);
            }
            
            return $extractResult;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    private function captureUsingHlsDirectWithUniqueness($hlsUrl, $outputFile, $format, &$usedSegments) {
        try {
            // Step 1: Get fresh segment URL, avoiding duplicates within this worker
            $segmentUrl = $this->getUniqueSegmentUrl($hlsUrl, $usedSegments);
            if (!$segmentUrl) {
                return [
                    'success' => false,
                    'error' => 'Failed to get unique segment URL from HLS playlist'
                ];
            }
            
            echo "Worker " . getmypid() . ": Using segment: " . basename($segmentUrl) . "\n";
            
            // Step 2: Download partial segment using Range requests (500KB)
            $tempPartialSegment = sys_get_temp_dir() . '/hls_partial_' . uniqid() . '.ts';
            $downloadResult = $this->downloadPartialSegment($segmentUrl, $tempPartialSegment);
            
            if (!$downloadResult['success']) {
                // Fallback: try without range if server doesn't support it
                $downloadResult = $this->downloadSegment($segmentUrl, $tempPartialSegment);
            }
            
            if (!$downloadResult['success']) {
                return [
                    'success' => false,
                    'error' => $downloadResult['error']
                ];
            }
            
            // Step 3: Extract I-frame and convert to JPEG with specified quality
            $extractResult = $this->extractIFrameToJpeg($tempPartialSegment, $outputFile, $format);
            
            // Cleanup
            if (file_exists($tempPartialSegment)) {
                unlink($tempPartialSegment);
            }
            
            return $extractResult;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    private function captureUsingHlsDirect($hlsUrl, $outputFile, $format) {
        try {
            // Step 1: Get random segment URL (no caching for uniqueness)
            $segmentUrl = $this->getRandomSegmentUrl($hlsUrl);
            if (!$segmentUrl) {
                return [
                    'success' => false,
                    'error' => 'Failed to get segment URL from HLS playlist'
                ];
            }
            
            echo "Worker " . getmypid() . ": Using random segment: " . basename($segmentUrl) . "\n";
            
            // Step 2: Download partial segment using Range requests (500KB)
            $tempPartialSegment = sys_get_temp_dir() . '/hls_partial_' . uniqid() . '.ts';
            $downloadResult = $this->downloadPartialSegment($segmentUrl, $tempPartialSegment);
            
            if (!$downloadResult['success']) {
                // Fallback: try without range if server doesn't support it
                $downloadResult = $this->downloadSegment($segmentUrl, $tempPartialSegment);
            }
            
            if (!$downloadResult['success']) {
                return [
                    'success' => false,
                    'error' => $downloadResult['error']
                ];
            }
            
            // Step 3: Extract I-frame and convert to JPEG with specified quality
            $extractResult = $this->extractIFrameToJpeg($tempPartialSegment, $outputFile, $format);
            
            // Cleanup
            if (file_exists($tempPartialSegment)) {
                unlink($tempPartialSegment);
            }
            
            return $extractResult;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get unique segment URL - takes the freshest unused segment
     */
    private function getUniqueSegmentUrl($hlsUrl, &$usedSegments) {
        $segments = $this->getAllSegmentsFromPlaylist($hlsUrl);
        
        if (empty($segments)) {
            echo "Worker " . getmypid() . ": No segments found in playlist\n";
            return null;
        }
        
        // Find unused segment
        $unusedSegments = array_diff($segments, $usedSegments);
        
        if (empty($unusedSegments)) {
            // If all segments used, clear the list and start fresh
            $usedSegments = [];
            $unusedSegments = $segments;
            echo "Worker " . getmypid() . ": Cleared used segments list, starting fresh\n";
        }
        
        // Get the freshest (last) segment from unused segments
        // HLS segments are ordered chronologically, so last = freshest
        $segmentUrl = end($unusedSegments);
        
        // Mark as used
        $usedSegments[] = $segmentUrl;
        
        echo "Worker " . getmypid() . ": Found " . count($segments) . " segments, selected freshest unused: " . basename($segmentUrl) . "\n";
        
        return $segmentUrl;
    }

    /**
     * Get random segment URL - no caching, fresh playlist every time
     * This ensures natural uniqueness without complex synchronization
     */
    private function getRandomSegmentUrl($hlsUrl) {
        $segments = $this->getAllSegmentsFromPlaylist($hlsUrl);
        
        if (empty($segments)) {
            echo "Worker " . getmypid() . ": No segments found in playlist\n";
            return null;
        }
        
        // Get random segment from available segments (better randomization)
        $randomIndex = random_int(0, count($segments) - 1);
        $segmentUrl = $segments[$randomIndex];
        
        echo "Worker " . getmypid() . ": Found " . count($segments) . " segments, selected index {$randomIndex}\n";
        
        return $segmentUrl;
    }

    /**
     * Get all segments from HLS playlist (not just the latest)
     * This builds a queue of segments for unique frame generation
     */
    private function getAllSegmentsFromPlaylist($hlsUrl) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $hlsUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 5,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'JPEG-HLS-Direct/1.0'
        ]);
        
        $playlistContent = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($playlistContent === false || $httpCode !== 200) {
            error_log("Failed to fetch HLS playlist: HTTP $httpCode");
            return [];
        }
        
        $lines = explode("\n", $playlistContent);
        $segments = [];
        $baseUrl = dirname($hlsUrl) . '/';
        
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line) || $line[0] === '#') {
                continue;
            }
            
            if (strpos($line, '.ts') !== false || strpos($line, '.m4s') !== false) {
                if (strpos($line, 'http') === 0) {
                    $segments[] = $line;
                } else {
                    $segments[] = $baseUrl . $line;
                }
            }
        }
        
        // Return all segments (for queue), not just the last one
        return $segments;
    }
    
    /**
     * Legacy method - get only the latest segment (for compatibility)
     */
    private function getLatestSegmentUrl($hlsUrl) {
        $segments = $this->getAllSegmentsFromPlaylist($hlsUrl);
        return empty($segments) ? null : end($segments);
    }

    private function downloadPartialSegment($segmentUrl, $outputPath, $maxBytes = 512000) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $segmentUrl,
            CURLOPT_RETURNTRANSFER => false,
            CURLOPT_FILE => fopen($outputPath, 'w'),
            CURLOPT_TIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_USERAGENT => 'JPEG-HLS-Direct/1.0',
            CURLOPT_RANGE => "0-" . ($maxBytes - 1),
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
        ]);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 206 && $httpCode !== 200) {
            return ['success' => false, 'error' => "Failed to download segment: HTTP $httpCode"];
        }
        
        if (!file_exists($outputPath) || filesize($outputPath) === 0) {
            return ['success' => false, 'error' => "Downloaded segment is empty"];
        }
        
        return ['success' => true, 'segment_size' => filesize($outputPath)];
    }

    private function downloadSegment($segmentUrl, $outputPath) {
        $command = sprintf('curl -s -L -o "%s" "%s"', $outputPath, $segmentUrl);
        exec($command . " 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0 || !file_exists($outputPath) || filesize($outputPath) === 0) {
            return ['success' => false, 'error' => "Failed to download segment"];
        }
        
        return ['success' => true, 'segment_size' => filesize($outputPath)];
    }

    private function extractIFrameToJpeg($segmentPath, $outputPath, $format) {
        $ffmpegPath = $this->config['screenshot']['ffmpeg_path'];
        
        // Special handling for MozJPEG formats with multi-step process
        if ($format['name'] === 'MozJPEG Post-Process') {
            return $this->extractUsingTrueMozJpeg($segmentPath, $outputPath, $ffmpegPath, $format['name']);
        }
        
        // Build FFmpeg command based on format template
        $command = sprintf(
            $format['command_template'],
            escapeshellcmd($ffmpegPath),
            $segmentPath,
            $outputPath
        );
        
        // Add I-frame selection filter ONLY if not explicitly skipped
        if (empty($format['skip_iframe_filter'])) {
            // This pattern will find -i "filepath" regardless of what comes before or after
            $pattern = '/(-i\s+"' . preg_quote($segmentPath, '/') . '")/';
            $replacement = '$1 -vf "select=\'eq(pict_type,I)\'"';
            $command = preg_replace($pattern, $replacement, $command);
        }
        
        // Debug: Log the final command for troubleshooting
        error_log("Worker " . getmypid() . ": Executing command: " . $command);
        
        exec($command . " 2>&1", $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($outputPath)) {
            return ['success' => true, 'file_size' => filesize($outputPath)];
        } else {
            // Debug: Log detailed error information
            error_log("Worker " . getmypid() . ": Command failed with return code $returnCode");
            error_log("Worker " . getmypid() . ": Command output: " . implode("\n", $output));
            
            return [
                'success' => false,
                'error' => 'Command failed: ' . implode(" ", array_slice($output, -2)),
                'return_code' => $returnCode,
                'full_output' => $output
            ];
        }
    }
    
    private function extractUsingTrueMozJpeg($segmentPath, $outputPath, $ffmpegPath, $formatName) {
        // POST-PROCESS: Basic JPEG -> MozJPEG recompression (like Squoosh workflow)
        $tempFile = $outputPath . '.tmp';
        $pipeCommand = sprintf(
            '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s" && /usr/local/bin/cjpeg -quality 75 -progressive -optimize -nojfif "%s" > "%s" && rm "%s"',
            escapeshellcmd($ffmpegPath),
            $segmentPath,
            $tempFile,
            $tempFile,
            $outputPath,
            $tempFile
        );
        error_log("Worker " . getmypid() . ": FFmpeg JPEG → MozJPEG (Post-process like Squoosh): " . $pipeCommand);
        
        exec($pipeCommand . " 2>&1", $output, $returnCode);
        
        if ($returnCode !== 0 || !file_exists($outputPath)) {
            return [
                'success' => false,
                'error' => 'Direct pipe FFmpeg→MozJPEG failed: ' . implode(" ", array_slice($output, -2)),
                'return_code' => $returnCode,
                'full_output' => $output
            ];
        }
        
        return ['success' => true, 'file_size' => filesize($outputPath)];
    }

    private function getCamera1() {
        try {
            $stmt = $this->db->prepare("SELECT * FROM cameras WHERE id = 1 LIMIT 1");
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting camera 1: " . $e->getMessage());
            return null;
        }
    }
}

// Main execution
if ($argc < 2) {
    echo "Usage: php jpeg_worker.php <base64_encoded_job_data>\n";
    exit(1);
}

try {
    $jobData = json_decode(base64_decode($argv[1]), true);
    if (!$jobData) {
        throw new Exception("Invalid job data");
    }
    
    $worker = new JpegWorker($jobData);
    $worker->execute();
    
} catch (Exception $e) {
    echo "RESULT_JSON_START\n";
    echo json_encode([
        'error' => $e->getMessage(),
        'success_count' => 0,
        'error_count' => 1,
        'worker_id' => getmypid()
    ], JSON_PRETTY_PRINT);
    echo "\nRESULT_JSON_END\n";
    
    error_log("JPEG Worker fatal error: " . $e->getMessage());
    exit(1);
}