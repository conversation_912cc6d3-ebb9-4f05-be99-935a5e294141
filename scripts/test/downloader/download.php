<?php

/**
 * Multi-threaded Image Downloader
 * 
 * Downloads images from Backblaze B2 storage using parallel processes.
 * 
 * Usage:
 *   php download.php                    # Use default 20 threads
 *   php download.php -t 10              # Use 10 threads  
 *   php download.php --threads=30       # Use 30 threads
 * 
 * Features:
 * - <PERSON><PERSON>l downloading in multiple processes
 * - Automatic retry logic for failed downloads
 * - Skip existing files to avoid re-downloading
 * - Organized camera-specific directory structure
 * - Minimal logging for performance
 */

namespace App;

// Определяем правильные пути для Docker окружения
$vendorPath = __DIR__ . '/../../vendor/autoload.php';
if (!file_exists($vendorPath)) {
    // Альтернативный путь в Docker
    $vendorPath = '/var/www/html/vendor/autoload.php';
}

require_once $vendorPath;

use PDO;
use Exception;
use Aws\S3\S3Client;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set max execution time to 1000 minutes
set_time_limit(60000);

class ImageDownloader {
    private $config;
    private $db;
    private $archiveClient;
    private $outputDir;

    // Конфигурация для скачивания
    private $ids = [1,2,3,4,5]; // Массив ID камер для скачивания
    private $limit = 200; // Количество последних изображений для каждой камеры (для тестирования)
    // private $limit = 100; // Количество последних изображений для каждой камеры (для тестирования)
    private $maxConcurrentDownloads = 20; // Количество параллельных потоков

    public function __construct(array $config) {
        $this->config = $config;
        $this->outputDir = __DIR__ . '/../../../screenshots';
        
        $this->initializeDatabase();
        $this->initializeArchiveClient();
        $this->ensureOutputDirectory();
    }

    /**
     * Set maximum number of concurrent downloads
     */
    public function setMaxConcurrentDownloads(int $maxThreads): void {
        $this->maxConcurrentDownloads = max(1, min($maxThreads, 50)); // Limit between 1 and 50
    }

    private function initializeDatabase() {
        try {
            $dsn = sprintf(
                "mysql:host=%s;dbname=%s;charset=%s",
                $this->config['database']['host'],
                $this->config['database']['dbname'],
                $this->config['database']['charset']
            );

            error_log("Connecting to database with DSN: " . $dsn);
            $this->db = new PDO(
                $dsn,
                $this->config['database']['username'],
                $this->config['database']['password'],
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            error_log("Database connection successful");
        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    private function initializeArchiveClient() {
        try {
            $this->archiveClient = new S3Client([
                'version' => 'latest',
                'region' => 'us-west-001',
                'endpoint' => $this->config['backblaze']['archive']['endpoint'],
                'credentials' => [
                    'key' => $this->config['backblaze']['archive']['application_key_id'],
                    'secret' => $this->config['backblaze']['archive']['application_key']
                ],
                'use_path_style_endpoint' => true,
                'signature_version' => 'v4',
                'http' => [
                    'verify' => false // Only use this in development, remove in production
                ],
                'bucket_endpoint' => false,
                'force_path_style' => true
            ]);
            error_log("Archive S3 client initialized successfully");
        } catch (Exception $e) {
            error_log("Failed to initialize archive S3 client: " . $e->getMessage());
            throw $e;
        }
    }

    private function ensureOutputDirectory() {
        // Create main output directory
        if (!file_exists($this->outputDir)) {
            if (!mkdir($this->outputDir, 0755, true)) {
                throw new Exception("Failed to create output directory: " . $this->outputDir);
            }
            error_log("Created output directory: " . $this->outputDir);
        }
        
        // Create directories for each camera
        foreach ($this->ids as $cameraId) {
            $cameraDir = $this->outputDir . "/camera_{$cameraId}";
            
            if (!file_exists($cameraDir)) {
                if (!mkdir($cameraDir, 0755, true)) {
                    throw new Exception("Failed to create camera directory: " . $cameraDir);
                }
                error_log("Created camera directory: " . $cameraDir);
            }
        }
    }

    /**
     * Ensure camera-specific directories exist
     *
     * @param array $cameraIds Array of camera IDs
     * @return void
     */
    private function ensureCameraDirectories(array $cameraIds) {
        foreach ($cameraIds as $cameraId) {
            $this->ensureCameraDirectory($cameraId);
        }
    }

    /**
     * Ensure specific camera directory exists
     *
     * @param int $cameraId Camera ID
     * @return void
     */
    private function ensureCameraDirectory(int $cameraId) {
        $cameraDir = $this->outputDir . "/camera_{$cameraId}";
        
        if (!file_exists($cameraDir)) {
            if (!mkdir($cameraDir, 0755, true)) {
                throw new Exception("Failed to create camera directory: " . $cameraDir);
            }
            error_log("Created camera directory: " . $cameraDir);
            echo "  📁 Created directory for camera {$cameraId}\n";
        }
    }

    public function downloadAllCameras() {
        echo "=== IMAGE DOWNLOADER TEST ===\n";
        echo "Downloading {$this->limit} latest images for cameras: " . implode(', ', $this->ids) . "\n\n";
        
        try {
            // Fetch camera information
            $placeholders = str_repeat('?,', count($this->ids) - 1) . '?';
            $stmt = $this->db->prepare("SELECT id, name FROM cameras WHERE id IN ({$placeholders}) ORDER BY id");
            $stmt->execute($this->ids);
            $cameras = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (empty($cameras)) {
                throw new Exception("No cameras found with IDs: " . implode(', ', $this->ids));
            }
            
            error_log("Found " . count($cameras) . " cameras to process");
            echo "Found " . count($cameras) . " cameras to process\n\n";

            foreach ($cameras as $camera) {
                echo "--- Processing camera: {$camera['name']} (ID: {$camera['id']}) ---\n";
                error_log("Processing camera: {$camera['name']} (ID: {$camera['id']})");
                
                try {
                    $this->downloadCameraImages($camera);
                    echo "✓ Successfully processed camera: {$camera['name']}\n\n";
                } catch (Exception $e) {
                    echo "✗ Failed to process camera {$camera['name']}: " . $e->getMessage() . "\n\n";
                    error_log("Failed to process camera {$camera['name']}: " . $e->getMessage());
                }
            }
            
            echo "=== DOWNLOAD COMPLETED ===\n";
            error_log("Image download test completed successfully");
            
        } catch (Exception $e) {
            error_log("Error in downloadAllCameras: " . $e->getMessage());
            echo "Error: " . $e->getMessage() . "\n";
            throw $e;
        }
    }

    private function downloadCameraImages(array $camera) {
        $cameraId = $camera['id'];
        $cameraName = $camera['name'];
        
        // Get latest images for this camera
        echo "  Getting latest {$this->limit} images from Backblaze...\n";
        $images = $this->getLatestImages($cameraId);
        
        if (empty($images)) {
            echo "  No images found for camera {$cameraName}\n";
            return;
        }

        echo "  Found " . count($images) . " images for camera {$cameraName}\n";
        
        // Check for existing images to avoid re-downloading
        $existingFiles = glob($this->outputDir . "/camera_{$cameraId}/camera_{$cameraId}_*.jpg");
        $existingCount = count($existingFiles);
        
        if ($existingCount > 0) {
            echo "  Found {$existingCount} existing images for camera {$cameraId}\n";
        }
        
        // Download images
        echo "  Downloading images...\n";
        $downloadedCount = $this->downloadImages($images, $cameraId, $cameraName);
        
        echo "  Downloaded: {$downloadedCount} images\n";
        echo "  Total images for camera {$cameraId}: " . count(glob($this->outputDir . "/camera_{$cameraId}/camera_{$cameraId}_*.jpg")) . "\n";
    }

    private function getLatestImages(int $cameraId): array {
        try {
            $prefix = "cameras/{$cameraId}/";
            error_log("Searching for images with prefix: " . $prefix);
            
            $allImages = [];
            $continuationToken = null;
            $totalObjectsProcessed = 0;
            $batchCount = 0;
            
            // Implement pagination to get ALL objects
            do {
                $batchCount++;
                $params = [
                    'Bucket' => $this->config['backblaze']['archive']['bucket_name'],
                    'Prefix' => $prefix,
                    'MaxKeys' => 1000 // Maximum allowed per request
                ];
                
                if ($continuationToken) {
                    $params['ContinuationToken'] = $continuationToken;
                }
                
                error_log("Fetching batch {$batchCount} for camera {$cameraId}...");
                echo "    Fetching batch {$batchCount} from Backblaze...\n";
                $objects = $this->archiveClient->listObjectsV2($params);
                
                if (empty($objects['Contents'])) {
                    if ($batchCount === 1) {
                        error_log("No objects found with prefix: " . $prefix);
                        return [];
                    }
                    break;
                }
                
                $batchObjectCount = count($objects['Contents']);
                error_log("Batch {$batchCount}: Found {$batchObjectCount} objects");
                echo "    Batch {$batchCount}: Found {$batchObjectCount} objects\n";
                $totalObjectsProcessed += $batchObjectCount;
                
                foreach ($objects['Contents'] as $object) {
                    // Skip if not a .jpg file
                    if (!preg_match('/\.jpg$/i', $object['Key'])) {
                        continue;
                    }
                    
                    $fileTimestamp = strtotime($object['LastModified']);
                    $allImages[] = [
                        'key' => $object['Key'],
                        'timestamp' => $fileTimestamp,
                        'lastModified' => $object['LastModified'],
                        'size' => $object['Size']
                    ];
                }
                
                // Check if there are more objects to fetch
                $continuationToken = $objects['NextContinuationToken'] ?? null;
                
            } while ($continuationToken);
            
            error_log("Total objects processed for camera {$cameraId}: {$totalObjectsProcessed} (in {$batchCount} batches)");
            error_log("Total valid .jpg images found: " . count($allImages));
            echo "  🔍 Total: {$totalObjectsProcessed} objects in {$batchCount} batches, " . count($allImages) . " .jpg images\n";

            // Sort by timestamp (newest first) and limit results
            if (!empty($allImages)) {
                usort($allImages, function($a, $b) {
                    return $b['timestamp'] - $a['timestamp'];
                });

                $latestImages = array_slice($allImages, 0, $this->limit);
                
                error_log("Filtered to " . count($latestImages) . " latest images for camera {$cameraId} (out of " . count($allImages) . " total)");
                
                if (!empty($latestImages)) {
                    $newest = date('Y-m-d H:i:s', $latestImages[0]['timestamp']);
                    $oldest = date('Y-m-d H:i:s', end($latestImages)['timestamp']);
                    error_log("Selected image range: {$oldest} to {$newest}");
                }

                return $latestImages;
            }
            
            error_log("No valid images found for camera {$cameraId}");
            return [];

        } catch (Exception $e) {
            error_log("Error getting latest images for camera {$cameraId}: " . $e->getMessage());
            throw $e;
        }
    }

    private function downloadImages(array $images, int $cameraId, string $cameraName): int {
        // Timing start
        $startTime = microtime(true);
        // Ensure camera directory exists (safety check)
        $this->ensureCameraDirectory($cameraId);
        
        // Filter out existing images first
        $imagesToDownload = [];
        $skippedCount = 0;
        
        foreach ($images as $image) {
            $originalFilename = basename($image['key']);
            $newFilename = "camera_{$cameraId}_{$originalFilename}";
            $localFilename = $this->outputDir . "/camera_{$cameraId}/" . $newFilename;
            
            // Skip if file already exists and has reasonable size
            if (file_exists($localFilename) && filesize($localFilename) > 1000) {
                $skippedCount++;
                continue;
            }
            
            $imagesToDownload[] = [
                'image' => $image,
                'localFilename' => $localFilename,
                'newFilename' => $newFilename
            ];
        }
        
        if ($skippedCount > 0) {
            echo "  Skipped {$skippedCount} existing images\n";
        }
        
        if (empty($imagesToDownload)) {
            echo "  No new images to download\n";
            return 0;
        }
        
        echo "  Starting multi-threaded download ({$this->maxConcurrentDownloads} threads) for " . count($imagesToDownload) . " images...\n";
        
        // Download images in parallel
        $downloadResults = $this->downloadImagesParallel($imagesToDownload, $cameraId);
        
        $downloadedCount = $downloadResults['downloaded'];
        $errorCount = $downloadResults['errors'];
        
        echo "  Multi-threaded download completed: {$downloadedCount} downloaded, {$errorCount} errors\n";
        
        // Timing end
        $endTime = microtime(true);
        $elapsed = $endTime - $startTime;
        printf("  Download time for camera %s: %.3f seconds\n", $cameraName, $elapsed);
        
        return $downloadedCount;
    }

    /**
     * Download images in parallel using multiple processes
     */
    private function downloadImagesParallel(array $imagesToDownload, int $cameraId): array {
        $imageChunks = array_chunk($imagesToDownload, ceil(count($imagesToDownload) / $this->maxConcurrentDownloads));
        $processes = [];
        $downloadedCount = 0;
        $errorCount = 0;
        
        foreach ($imageChunks as $chunkIndex => $chunk) {
            $command = $this->buildDownloadCommand($chunk, $cameraId, $chunkIndex);
            
            $process = proc_open($command, [
                0 => ['pipe', 'r'],  // stdin
                1 => ['pipe', 'w'],  // stdout
                2 => ['pipe', 'w']   // stderr
            ], $pipes);
            
            if (is_resource($process)) {
                $processes[] = [
                    'process' => $process,
                    'pipes' => $pipes,
                    'chunk_index' => $chunkIndex,
                    'image_count' => count($chunk)
                ];
            }
        }
        
        // Wait for all processes to complete and collect results
        foreach ($processes as $processData) {
            $output = stream_get_contents($processData['pipes'][1]);
            $errors = stream_get_contents($processData['pipes'][2]);
            
            fclose($processData['pipes'][0]);
            fclose($processData['pipes'][1]);
            fclose($processData['pipes'][2]);
            
            $returnValue = proc_close($processData['process']);
            
            // Parse results from output
            if (preg_match('/DOWNLOADED:(\d+)/', $output, $matches)) {
                $downloadedCount += intval($matches[1]);
            }
            if (preg_match('/ERRORS:(\d+)/', $output, $matches)) {
                $errorCount += intval($matches[1]);
            }
            
            if ($returnValue !== 0 && !empty($errors)) {
                error_log("Process chunk {$processData['chunk_index']} error: " . $errors);
            }
        }
        
        return [
            'downloaded' => $downloadedCount,
            'errors' => $errorCount
        ];
    }

    /**
     * Build command for downloading a chunk of images
     */
    private function buildDownloadCommand(array $imageChunk, int $cameraId, int $chunkIndex): string {
        $tempFile = tempnam(sys_get_temp_dir(), "download_chunk_{$chunkIndex}_");
        file_put_contents($tempFile, serialize([
            'images' => $imageChunk,
            'config' => $this->config,
            'outputDir' => $this->outputDir,
            'cameraId' => $cameraId
        ]));
        
        $scriptPath = __DIR__ . '/download_worker.php';
        return "php '{$scriptPath}' '{$tempFile}'";
    }

    public function getStatistics() {
        echo "\n=== DOWNLOAD STATISTICS ===\n";
        
        $totalImages = 0;
        $totalSize = 0;
        
        foreach ($this->ids as $cameraId) {
            $imageFiles = glob($this->outputDir . "/camera_{$cameraId}/camera_{$cameraId}_*.jpg");
            $imageCount = count($imageFiles);
            $cameraSize = 0;
            
            foreach ($imageFiles as $file) {
                $cameraSize += filesize($file);
            }
            
            $totalImages += $imageCount;
            $totalSize += $cameraSize;
            
            echo "Camera {$cameraId}: {$imageCount} images, " . round($cameraSize / 1024 / 1024, 2) . " MB\n";
        }
        
        echo "TOTAL: {$totalImages} images, " . round($totalSize / 1024 / 1024, 2) . " MB\n";
        echo "Average image size: " . round($totalSize / max($totalImages, 1) / 1024, 1) . " KB\n";
        
        // Show directory structure
        echo "\n=== DIRECTORY STRUCTURE ===\n";
        echo "screenshots/ - {$totalImages} files total in " . count($this->ids) . " camera folders\n";
        foreach ($this->ids as $cameraId) {
            $cameraDir = $this->outputDir . "/camera_{$cameraId}";
            $cameraFiles = glob($this->outputDir . "/camera_{$cameraId}/camera_{$cameraId}_*.jpg");
            $dirExists = is_dir($cameraDir);
            $fileCount = count($cameraFiles);
            
            if ($dirExists || $fileCount > 0) {
                echo "  📁 camera_{$cameraId}/ - {$fileCount} files";
                if (!$dirExists) {
                    echo " (directory missing!)";
                }
                echo "\n";
            }
        }
    }
}

// Main execution
try {
    echo "Multi-threaded Image Downloader\n";
    echo str_repeat("=", 50) . "\n\n";
    
    // Parse command line arguments
    $options = getopt('t:', ['threads:']);
    $threads = $options['t'] ?? $options['threads'] ?? 20;
    
    // Определяем правильный путь к config.php для Docker окружения
    $configPath = __DIR__ . '/../../config.php';
    if (!file_exists($configPath)) {
        // Альтернативный путь в Docker
        $configPath = '/var/www/html/config.php';
    }
    
    if (!file_exists($configPath)) {
        throw new Exception("Config file not found. Checked paths: " . __DIR__ . '/../../config.php and /var/www/html/config.php');
    }
    
    $config = require $configPath;
    $downloader = new ImageDownloader($config);
    
    // Set number of threads
    $downloader->setMaxConcurrentDownloads($threads);
    echo "Using {$threads} parallel download threads\n\n";
    
    // Run the download
    $downloader->downloadAllCameras();
    
    // Show statistics of downloads
    $downloader->getStatistics();
    
} catch (Exception $e) {
    error_log("Error in download script: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}