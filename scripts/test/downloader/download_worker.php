<?php

/**
 * Multi-threaded Image Download Worker
 * 
 * This script is spawned by the main download.php to handle image downloads
 * in parallel processes. Each worker handles a chunk of images.
 */

// Check if temp file argument is provided
if ($argc < 2) {
    error_log("Usage: download_worker.php <temp_file_path>");
    exit(1);
}

$tempFile = $argv[1];

if (!file_exists($tempFile)) {
    error_log("Temp file not found: " . $tempFile);
    exit(1);
}

try {
    // Load serialized data
    $data = unserialize(file_get_contents($tempFile));
    $images = $data['images'];
    $config = $data['config'];
    $outputDir = $data['outputDir'];
    $cameraId = $data['cameraId'];
    
    // Clean up temp file
    unlink($tempFile);
    
    // Initialize S3 client for this worker
    $vendorPath = __DIR__ . '/../../vendor/autoload.php';
    if (!file_exists($vendorPath)) {
        $vendorPath = '/var/www/html/vendor/autoload.php';
    }
    require_once $vendorPath;
    
    $archiveClient = new \Aws\S3\S3Client([
        'version' => 'latest',
        'region' => 'us-west-001',
        'endpoint' => $config['backblaze']['archive']['endpoint'],
        'credentials' => [
            'key' => $config['backblaze']['archive']['application_key_id'],
            'secret' => $config['backblaze']['archive']['application_key']
        ],
        'use_path_style_endpoint' => true,
        'signature_version' => 'v4',
        'http' => [
            'verify' => false
        ],
        'bucket_endpoint' => false,
        'force_path_style' => true
    ]);
    
    $downloadedCount = 0;
    $errorCount = 0;
    
    foreach ($images as $imageData) {
        try {
            $image = $imageData['image'];
            $localFilename = $imageData['localFilename'];
            $newFilename = $imageData['newFilename'];
            
            // Generate presigned URL for download
            $command = $archiveClient->getCommand('GetObject', [
                'Bucket' => $config['backblaze']['archive']['bucket_name'],
                'Key' => $image['key']
            ]);

            $presignedRequest = $archiveClient->createPresignedRequest($command, '+10 minutes');
            $downloadUrl = (string) $presignedRequest->getUri();

            // Download image with retry logic
            $maxRetries = 3;
            $imageContent = false;
            
            for ($retry = 1; $retry <= $maxRetries; $retry++) {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 30,
                        'user_agent' => 'ImageDownloader/1.0'
                    ]
                ]);
                
                $imageContent = @file_get_contents($downloadUrl, false, $context);
                if ($imageContent !== false) {
                    break;
                } else {
                    if ($retry < $maxRetries) {
                        usleep(500000); // Wait 0.5 seconds before retry
                    }
                }
            }
            
            if ($imageContent === false) {
                $errorCount++;
                continue;
            }

            // Save image
            $bytesWritten = file_put_contents($localFilename, $imageContent);
            if ($bytesWritten === false) {
                $errorCount++;
                continue;
            }
            
            $downloadedCount++;
            
        } catch (\Exception $e) {
            $errorCount++;
            continue;
        }
    }
    
    // Output results in parseable format
    echo "DOWNLOADED:{$downloadedCount}\n";
    echo "ERRORS:{$errorCount}\n";
    
    exit(0);
    
} catch (\Exception $e) {
    error_log("Worker error: " . $e->getMessage());
    echo "DOWNLOADED:0\n";
    echo "ERRORS:1\n";
    exit(1);
} 