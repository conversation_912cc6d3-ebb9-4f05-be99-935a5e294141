-- Migration: Create screenshot failures tracking table
-- Version: 002
-- Description: Creates table for logging failed screenshot capture attempts
-- Purpose: Monitor and analyze screenshot creation failures for retry logic and debugging

-- Screenshot failure tracking table
CREATE TABLE IF NOT EXISTS screenshot_failures (
    id INT PRIMARY KEY AUTO_INCREMENT,
    camera_id INT NOT NULL,
    job_id INT NULL COMMENT 'Reference to async_jobs.id if failure occurred during async job',
    attempt_number INT NOT NULL COMMENT 'Which attempt this was (1-10)',
    total_attempts INT NOT NULL COMMENT 'Total number of attempts configured',
    failure_reason TEXT NOT NULL COMMENT 'Human-readable reason for the failure',
    hls_url VARCHAR(500) NULL COMMENT 'HLS stream URL that was being processed',
    output_file VARCHAR(255) NULL COMMENT 'Intended output filename',
    file_size_bytes INT NULL COMMENT 'Size of corrupted/failed file in bytes (if any)',
    command_executed TEXT NULL COMMENT 'Full command that was executed',
    error_output TEXT NULL COMMENT 'Error output from command execution',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for performance and analysis
    INDEX idx_camera_id (camera_id),
    INDEX idx_job_id (job_id),
    INDEX idx_created_at (created_at),
    INDEX idx_camera_created (camera_id, created_at),
    INDEX idx_attempt_number (attempt_number),
    INDEX idx_failure_reason (failure_reason(100))
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='Tracks failed screenshot capture attempts for monitoring and analysis'; 