-- Migration: Create video_failures table
-- Description: Track failed video generation attempts for debugging and monitoring
-- Version: 003
-- Date: 2025-01-17

CREATE TABLE IF NOT EXISTS video_failures (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    camera_id INT NOT NULL,
    job_id VARCHAR(255) NULL, -- Job identifier for tracking
    attempt_number INT NOT NULL DEFAULT 1,
    total_attempts INT NOT NULL DEFAULT 1,
    failure_reason ENUM(
        'ffmpeg_failed',
        'file_not_created',
        'file_too_small',
        'no_source_frames',
        'directory_not_found',
        'symlink_creation_failed',
        'upload_failed',
        'unknown'
    ) NOT NULL DEFAULT 'unknown',
    
    -- Video generation details
    period_type VARCHAR(10) NOT NULL, -- '1d', '7d', '30d'
    source_frames_count INT NULL,
    expected_output_file VARCHAR(500) NULL,
    file_size_bytes BIGINT NULL DEFAULT 0,
    
    -- FFmpeg command and error details
    ffmpeg_command TEXT NULL,
    ffmpeg_output TEXT NULL,
    error_message TEXT NULL,
    execution_time_seconds DECIMAL(8,3) NULL,
    
    -- Timing
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indexes for efficient querying
    INDEX idx_camera_created (camera_id, created_at),
    INDEX idx_job_id (job_id),
    INDEX idx_failure_reason (failure_reason),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 