<?php

namespace App\AsyncJobs;

use Exception;
use Guz<PERSON>Http\Client;

/**
 * Screenshot Job Worker
 * 
 * Handles asynchronous screenshot capture for individual cameras.
 * Implements the optimized ffmpeg + cjpeg command pipeline.
 */
class ScreenshotJobWorker extends JobWorker {
    private Client $httpClient;
    private array $tempFiles = [];
    private array $uploadDataCache = []; // Cache for B2 upload URLs and tokens
    
    // JPEG quality for all image variations (must match base screenshot quality)
    private const JPEG_QUALITY = 75;

    public function __construct(array $jobData) {
        parent::__construct($jobData);
        
        // Initialize HTTP client for logo downloads
        $this->httpClient = new Client([
            'timeout' => 30,
            'connect_timeout' => 10
        ]);
        
        // Validate required configuration
        $this->validateConfig([
            'async_jobs.screenshot.ffmpeg_path',
            'async_jobs.screenshot.output_dir',
            'async_jobs.screenshot.command_template'
        ]);
    }

    /**
     * Execute screenshot capture for the assigned camera
     */
    public function execute(): array {
        $camera = $this->getCameraInfo();
        $timestamp = time();
        
        $this->logProgress("Starting screenshot capture", [
            'camera_name' => $camera['name'],
            'hls_url' => $camera['hls_url']
        ]);

        // Create output directory and camera subdirectory
        $outputDir = $this->getConfigValue('async_jobs.screenshot.output_dir');
        $this->ensureDirectoryExists($outputDir);
        $this->ensureCameraDirectory($outputDir, $this->cameraId);

        // Generate base screenshot
        $baseScreenshot = $this->captureBaseScreenshot($camera, $outputDir, $timestamp);

        // Generate image variations
        $variations = $this->generateImageVariations($baseScreenshot, $camera, $outputDir, $timestamp);

        // Upload to Backblaze (if enabled)
        $uploadResults = [];
        if ($this->getConfigValue('async_jobs.screenshot.upload_to_backblaze', false)) {
            $uploadResults = $this->uploadToBackblaze($variations, $camera, $timestamp);
        } else {
            $this->logger->info("Backblaze upload disabled (safety)", $this->jobId);
        }
        
        // Update database with thumbnail URL
        $this->updateCameraThumbnail($camera, $variations, $uploadResults);
        
        // Clean up files based on upload status
        $this->cleanupFiles($this->tempFiles);
        
        // If upload was successful, clean up local screenshot files
        if ($this->getConfigValue('async_jobs.screenshot.upload_to_backblaze', false) && !empty($uploadResults)) {
            $this->cleanupLocalFiles($variations);
            $this->logger->info("Local screenshot files cleaned up after successful upload", $this->jobId);
        } else {
            $this->logger->info("Local screenshot files preserved (upload disabled or failed)", $this->jobId);
        }
        
        $this->logProgress("Screenshot capture completed successfully", [
            'variations_created' => count($variations),
            'uploads_completed' => count($uploadResults)
        ]);

        return [
            'camera_id' => $this->cameraId,
            'camera_name' => $camera['name'],
            'timestamp' => $timestamp,
            'base_screenshot' => basename($baseScreenshot),
            'variations' => array_map('basename', $variations),
            'upload_results' => $uploadResults,
            'temp_files_cleaned' => count($this->tempFiles)
        ];
    }

    /**
     * Ensure camera-specific directory exists
     *
     * @param string $outputDir Base output directory
     * @param int $cameraId Camera ID
     * @return void
     */
    private function ensureCameraDirectory(string $outputDir, int $cameraId): void {
        $cameraDir = $outputDir . "/camera_{$cameraId}";
        
        if (!file_exists($cameraDir)) {
            if (!mkdir($cameraDir, 0755, true)) {
                throw new Exception("Failed to create camera directory: " . $cameraDir);
            }
            $this->logger->debug("Created camera directory: " . $cameraDir, $this->jobId);
        }
    }

    /**
     * Get camera-specific output directory path
     *
     * @param string $outputDir Base output directory
     * @param int $cameraId Camera ID
     * @return string
     */
    private function getCameraOutputDir(string $outputDir, int $cameraId): string {
        return $outputDir . "/camera_{$cameraId}";
    }

    /**
     * Capture base screenshot using optimized ffmpeg + cjpeg pipeline with retry logic
     */
    private function captureBaseScreenshot(array $camera, string $outputDir, int $timestamp): string {
        $cameraOutputDir = $this->getCameraOutputDir($outputDir, $this->cameraId);
        $outputFile = $cameraOutputDir . '/' . $this->generateFilename('camera', 'jpg', $timestamp);
        
        // Get command template and paths
        $commandTemplate = $this->getConfigValue('async_jobs.screenshot.command_template');
        $ffmpegPath = $this->getConfigValue('async_jobs.screenshot.ffmpeg_path');
        $cjpegPath = $this->getConfigValue('async_jobs.screenshot.cjpeg_path', '/usr/local/bin/cjpeg');
        
        // Build the optimized command
        // Template: '%s -i "%s" -vframes 1 -vf "select=\'eq(pict_type,I)\'" -c:v mjpeg -q:v 5 -f mjpeg "%s.tmp" && %s -quality 75 -progressive -optimize -nojfif "%s.tmp" > "%s" && rm "%s.tmp"'
        $command = sprintf(
            $commandTemplate,
            escapeshellcmd($ffmpegPath),    // %s - ffmpeg path
            $camera['hls_url'],             // %s - input HLS URL
            $outputFile,                    // %s - base output file (template adds .tmp)
            escapeshellcmd($cjpegPath),     // %s - cjpeg path
            $outputFile,                    // %s - base file for .tmp input to cjpeg
            $outputFile,                    // %s - final output file
            $outputFile                     // %s - base file for .tmp removal
        );
        
        // Retry logic - get parameters from config
        $retryConfig = $this->getConfigValue('async_jobs.screenshot.retry', []);
        $maxAttempts = $retryConfig['base_screenshot_max_attempts'] ?? 10;
        $minFileSize = $retryConfig['min_file_size_bytes'] ?? 1000;
        $waitTimes = $retryConfig['wait_between_attempts_seconds'] ?? [2, 4, 6, 8, 10];
        $lastException = null;
        
        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                $this->logProgress("Executing screenshot command", [
                    'attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'output_file' => basename($outputFile),
                    'camera_dir' => "camera_{$this->cameraId}"
                ]);
                
                // Clean up any previous failed attempt
                if (file_exists($outputFile)) {
                    unlink($outputFile);
                }
                if (file_exists($outputFile . '.tmp')) {
                    unlink($outputFile . '.tmp');
                }
                
                $result = $this->executeCommand($command, "Screenshot capture (attempt {$attempt})");
                
                if (!$result['success']) {
                    $errorMessage = "Command failed: " . implode("\n", $result['output']);
                    $this->logScreenshotFailure($camera, $attempt, $maxAttempts, $errorMessage, $outputFile, $command, $result['output']);
                    throw new Exception($errorMessage);
                }
                
                // Validate file was created and has reasonable size
                if (!file_exists($outputFile)) {
                    $errorMessage = "Screenshot file was not created: " . $outputFile;
                    $this->logScreenshotFailure($camera, $attempt, $maxAttempts, $errorMessage, $outputFile, $command, ['File not created']);
                    throw new Exception($errorMessage);
                }
                
                $fileSize = filesize($outputFile);
                if ($fileSize < $minFileSize) {
                    $errorMessage = "Screenshot file is too small: {$fileSize} bytes (minimum {$minFileSize} bytes)";
                    $this->logScreenshotFailure($camera, $attempt, $maxAttempts, $errorMessage, $outputFile, $command, ["File size: {$fileSize} bytes"]);
                    throw new Exception($errorMessage);
                }
                
                // Success! Log and return
                $this->logger->info("Base screenshot captured successfully", $this->jobId, [
                    'attempt' => $attempt,
                    'file_size' => $fileSize,
                    'execution_time' => $result['execution_time'],
                    'camera_directory' => "camera_{$this->cameraId}"
                ]);
                
                return $outputFile;
                
            } catch (Exception $e) {
                $lastException = $e;
                
                if ($attempt < $maxAttempts) {
                    $this->logger->warning("Screenshot attempt {$attempt} failed, retrying", $this->jobId, [
                        'error' => $e->getMessage(),
                        'remaining_attempts' => $maxAttempts - $attempt
                    ]);
                    
                    // Wait before retry (use configured wait times)
                    $waitIndex = min($attempt - 1, count($waitTimes) - 1);
                    $waitSeconds = $waitTimes[$waitIndex];
                    sleep($waitSeconds);
                } else {
                    // All attempts failed
                    $this->logger->error("All {$maxAttempts} screenshot attempts failed", $this->jobId, [
                        'final_error' => $e->getMessage(),
                        'camera_name' => $camera['name'],
                        'hls_url' => $camera['hls_url']
                    ]);
                }
            }
        }
        
        // If we get here, all attempts failed
        throw new Exception("Screenshot capture failed after {$maxAttempts} attempts. Last error: " . $lastException->getMessage());
    }

    /**
     * Generate image variations (logo overlay, thumbnails)
     */
    private function generateImageVariations(string $baseScreenshot, array $camera, string $outputDir, int $timestamp): array {
        $variations = ['original' => $baseScreenshot];
        $imageConfig = $this->getConfigValue('async_jobs.screenshot.image_variations', []);
        $retryConfig = $this->getConfigValue('async_jobs.screenshot.retry', []);
        $cameraOutputDir = $this->getCameraOutputDir($outputDir, $this->cameraId);
        
        $maxAttempts = $retryConfig['variation_max_attempts'] ?? 3;
        $minFileSize = $retryConfig['min_file_size_bytes'] ?? 1000;
        $cleanupOnFailure = $retryConfig['cleanup_on_total_failure'] ?? true;
        $createdFiles = [$baseScreenshot]; // Track all created files for cleanup
        
        // Logo overlay with retry logic
        if ($imageConfig['logo_overlay']['enabled'] ?? false) {
            $logoOverlaySuccess = false;
            
            for ($attempt = 1; $attempt <= $maxAttempts && !$logoOverlaySuccess; $attempt++) {
                try {
                    $logoPath = $this->createLogoOverlay($baseScreenshot, $camera, $cameraOutputDir, $timestamp);
                    
                    // Validate logo overlay file
                    if (file_exists($logoPath) && filesize($logoPath) > $minFileSize) {
                        $variations['logo_overlay'] = $logoPath;
                        $createdFiles[] = $logoPath;
                        $logoOverlaySuccess = true;
                        
                        $this->logger->info("Logo overlay created successfully", $this->jobId, [
                            'attempt' => $attempt,
                            'file_size' => filesize($logoPath)
                        ]);
                    } else {
                        throw new Exception("Logo overlay file was not created or is too small");
                    }
                    
                } catch (Exception $e) {
                    $this->logger->warning("Logo overlay attempt {$attempt} failed: " . $e->getMessage(), $this->jobId);
                    
                    if ($attempt < $maxAttempts) {
                        sleep(1); // Brief pause before retry
                    }
                }
            }
            
            if (!$logoOverlaySuccess) {
                if ($cleanupOnFailure) {
                    $this->logger->error("Logo overlay failed completely, cleaning up all files", $this->jobId);
                    $this->cleanupCreatedFiles($createdFiles);
                    throw new Exception("Logo overlay creation failed after {$maxAttempts} attempts");
                } else {
                    $this->logger->warning("All logo overlay attempts failed, using original image as fallback", $this->jobId);
                    $variations['logo_overlay'] = $baseScreenshot; // Use original as fallback
                }
            }
        }
        
        // Thumbnail variations - dynamically get from config
        foreach ($imageConfig as $variationType => $config) {
            // Skip logo overlay as it's handled separately
            if ($variationType === 'logo_overlay') {
                continue;
            }
            
            if ($config['enabled'] ?? false) {
                $width = $config['width'] ?? null;
                $height = $config['height'] ?? null;
                
                if (!$width || !$height) {
                    $this->logger->warning("Missing width or height for variation type: {$variationType}", $this->jobId);
                    continue;
                }
                
                $thumbnailSuccess = false;
                
                for ($thumbAttempt = 1; $thumbAttempt <= $maxAttempts && !$thumbnailSuccess; $thumbAttempt++) {
                    try {
                        $sourceImage = $variations['logo_overlay'] ?? $baseScreenshot;
                        $thumbPath = $this->resizeImage($sourceImage, $width, $height, $variationType, $timestamp, $cameraOutputDir);
                        
                        // Validate thumbnail file (thumbnails can be smaller)
                        $minThumbSize = max(500, $minFileSize / 2);
                        if (file_exists($thumbPath) && filesize($thumbPath) > $minThumbSize) {
                            $variations[$variationType] = $thumbPath;
                            $createdFiles[] = $thumbPath;
                            $thumbnailSuccess = true;
                            
                            $this->logger->debug("Thumbnail created successfully", $this->jobId, [
                                'type' => $variationType,
                                'attempt' => $thumbAttempt,
                                'file_size' => filesize($thumbPath)
                            ]);
                        } else {
                            throw new Exception("Thumbnail file was not created or is too small");
                        }
                        
                    } catch (Exception $e) {
                        $this->logger->warning("Thumbnail creation attempt {$thumbAttempt} failed for {$variationType}: " . $e->getMessage(), $this->jobId);
                        
                        if ($thumbAttempt < $maxAttempts) {
                            sleep(1); // Brief pause before retry
                        }
                    }
                }
                
                if (!$thumbnailSuccess) {
                    if ($cleanupOnFailure) {
                        $this->logger->error("Thumbnail creation failed completely for {$variationType}, cleaning up all files", $this->jobId);
                        $this->cleanupCreatedFiles($createdFiles);
                        throw new Exception("Thumbnail creation failed for {$variationType} after {$maxAttempts} attempts");
                    } else {
                        $this->logger->warning("All thumbnail creation attempts failed for {$variationType}", $this->jobId);
                    }
                }
            }
        }
        
        return $variations;
    }

    /**
     * Create logo overlay using ImageMagick
     */
    private function createLogoOverlay(string $baseImage, array $camera, string $cameraOutputDir, int $timestamp): string {
        if (!extension_loaded('imagick')) {
            throw new Exception("ImageMagick extension not available");
        }
        
        // Clean camera name for logo URL
        $cleanCameraName = str_replace(' ', '', $camera['name']);
        $logoUrl = "https://deployedweb.com/link_logos/overlays/{$cleanCameraName}.png";
        
        // Download logo
        $logoPath = tempnam(sys_get_temp_dir(), 'logo_');
        $this->tempFiles[] = $logoPath;
        
        $response = $this->httpClient->get($logoUrl, ['sink' => $logoPath]);
        
        if ($response->getStatusCode() !== 200) {
            throw new Exception("Failed to download logo for camera {$camera['name']}");
        }
        
        // Create overlay
        $base = new \Imagick($baseImage);
        $logo = new \Imagick($logoPath);
        
        // Scale logo to match base image dimensions
        $baseWidth = $base->getImageWidth();
        $baseHeight = $base->getImageHeight();
        $logo->scaleImage($baseWidth, $baseHeight, true);
        
        // Overlay the logo
        $base->compositeImage($logo, \Imagick::COMPOSITE_OVER, 0, 0);
        
        // Set JPEG quality to match base screenshot compression
        $base->setImageCompressionQuality(self::JPEG_QUALITY);
        $base->setImageFormat('jpeg');
        
        // Save result to camera directory
        $resultFilename = $this->generateFilename('logo_overlay', 'jpg', $timestamp);
        $resultPath = $cameraOutputDir . '/' . $resultFilename;
        $base->writeImage($resultPath);
        
        // Clean up
        $base->clear();
        $logo->clear();
        
        $this->logger->debug("Logo overlay created", $this->jobId, [
            'logo_url' => $logoUrl,
            'result_size' => filesize($resultPath),
            'saved_to' => $resultFilename,
            'jpeg_quality' => self::JPEG_QUALITY
        ]);
        
        return $resultPath;
    }

    /**
     * Resize image using ImageMagick
     */
    private function resizeImage(string $sourcePath, int $width, int $height, string $type, int $timestamp, string $cameraOutputDir): string {
        if (!extension_loaded('imagick')) {
            throw new Exception("ImageMagick extension not available");
        }
        
        $image = new \Imagick($sourcePath);
        $image->scaleImage($width, $height, true);
        
        // Set JPEG quality to match base screenshot compression
        $image->setImageCompressionQuality(self::JPEG_QUALITY);
        $image->setImageFormat('jpeg');
        
        $resizedFilename = $this->generateFilename($type, 'jpg', $timestamp);
        $resizedPath = $cameraOutputDir . '/' . $resizedFilename;
        $image->writeImage($resizedPath);
        $image->clear();
        
        $this->logger->debug("Image resized", $this->jobId, [
            'type' => $type,
            'dimensions' => "{$width}x{$height}",
            'result_size' => filesize($resizedPath),
            'saved_to' => $resizedFilename,
            'jpeg_quality' => self::JPEG_QUALITY
        ]);
        
        return $resizedPath;
    }

    /**
     * Upload variations to Backblaze
     */
    private function uploadToBackblaze(array $variations, array $camera, int $timestamp): array {
        $uploadResults = [];
        
        try {
            // Upload to archive bucket (main screenshot)
            $archiveFileName = sprintf("cameras/%d/%d.jpg", $camera['id'], $timestamp);
            $uploadResults['archive'] = $this->uploadFile($variations['original'], $archiveFileName, 'video-archive');
            
            // Upload to thumbnail bucket (latest screenshot)
            $thumbnailFileName = sprintf("cameras/%d/latest.jpg", $camera['id']);
            $uploadResults['thumbnail'] = $this->uploadFile($variations['original'], $thumbnailFileName, 'video-archive');
            
            // Upload logo overlay variations
            if (isset($variations['logo_overlay'])) {
                $wbrcFileName = sprintf("cameras/%d/wbrc.jpg", $camera['id']);
                $uploadResults['wbrc'] = $this->uploadFile($variations['logo_overlay'], $wbrcFileName, 'video-archive');
            }
            
            // Upload thumbnail variations to match ScreenshotCapture behavior
            if (isset($variations['thumbnail_720p'])) {
                $wbrc720pFileName = sprintf("cameras/%d/wbrc_720p.jpg", $camera['id']);
                $uploadResults['wbrc_720p'] = $this->uploadFile($variations['thumbnail_720p'], $wbrc720pFileName, 'video-archive');
            }
            
            if (isset($variations['thumbnail_low'])) {
                $wbrcLowFileName = sprintf("cameras/%d/wbrc_low.jpg", $camera['id']);
                $uploadResults['wbrc_low'] = $this->uploadFile($variations['thumbnail_low'], $wbrcLowFileName, 'video-archive');
            }
            
            $this->logger->info("Backblaze upload completed successfully", $this->jobId, [
                'uploads_count' => count($uploadResults)
            ]);
            
        } catch (Exception $e) {
            $this->logger->error("Backblaze upload failed: " . $e->getMessage(), $this->jobId);
            throw $e;
        }
        
        return $uploadResults;
    }
    
    /**
     * Upload single file to Backblaze B2
     */
    private function uploadFile(string $localFile, string $remotePath, string $bucketType): array {
        try {
            // Get upload data for the specified bucket type
            $uploadData = $this->getUploadData($bucketType);
            
            // Read file content and calculate SHA1
            $fileContent = file_get_contents($localFile);
            if ($fileContent === false) {
                throw new Exception("Failed to read file: " . $localFile);
            }
            
            $sha1 = sha1($fileContent);
            $contentType = 'image/jpeg';
            $fileSize = strlen($fileContent);
            
            $this->logger->debug("Starting B2 upload", $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'upload_url' => substr($uploadData['uploadUrl'], 0, 50) . '...'
            ]);
            
            // Upload to Backblaze B2
            $response = $this->httpClient->post($uploadData['uploadUrl'], [
                'headers' => [
                    'Authorization' => $uploadData['authorizationToken'],
                    'X-Bz-File-Name' => $remotePath,
                    'Content-Type' => $contentType,
                    'X-Bz-Content-Sha1' => $sha1,
                    'Content-Length' => $fileSize
                ],
                'body' => $fileContent,
                'timeout' => 300, // 5 minutes timeout for large files
                'http_errors' => false
            ]);
            
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            
            if ($statusCode !== 200) {
                throw new Exception("B2 upload failed with status {$statusCode}: " . $responseBody);
            }
            
            $responseData = json_decode($responseBody, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Failed to decode B2 response: " . json_last_error_msg());
            }
            
            $this->logger->info("B2 upload completed successfully", $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $responseData['fileId'] ?? 'unknown'
            ]);
            
            return [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $responseData['fileId'] ?? null,
                'upload_timestamp' => $responseData['uploadTimestamp'] ?? null,
                'uploaded_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            $this->logger->error("B2 upload failed: " . $e->getMessage(), $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'local_file' => basename($localFile)
            ]);
            throw $e;
        }
    }
    
    /**
     * Get upload data (URL and token) for specified bucket type
     */
    private function getUploadData(string $bucketType): array {
        $cacheKey = "b2_upload_data_{$bucketType}";
        
        // Check if we have cached upload data (in production, use Redis/Memcached)
        if (isset($this->uploadDataCache[$cacheKey])) {
            return $this->uploadDataCache[$cacheKey];
        }
        
        try {
            // Get B2 credentials
            $keyId = trim($this->getConfigValue("backblaze.{$bucketType}.application_key_id"));
            $key = trim($this->getConfigValue("backblaze.{$bucketType}.application_key"));
            $bucketId = trim($this->getConfigValue("backblaze.{$bucketType}.bucket_id"));
            
            if (empty($keyId) || empty($key) || empty($bucketId)) {
                throw new Exception("Missing B2 credentials for {$bucketType} bucket");
            }
            
            // Authorize account
            $authHeader = 'Basic ' . base64_encode($keyId . ':' . $key);
            
            $response = $this->httpClient->get('https://api.backblazeb2.com/b2api/v2/b2_authorize_account', [
                'headers' => [
                    'Authorization' => $authHeader,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 30,
                'http_errors' => false
            ]);
            
            if ($response->getStatusCode() !== 200) {
                throw new Exception("B2 authorization failed for {$bucketType}: " . $response->getBody()->getContents());
            }
            
            $authData = json_decode($response->getBody()->getContents(), true);
            if (!isset($authData['authorizationToken']) || !isset($authData['apiUrl'])) {
                throw new Exception("Invalid B2 authorization response for {$bucketType}");
            }
            
            // Get upload URL
            $response = $this->httpClient->post($authData['apiUrl'] . '/b2api/v2/b2_get_upload_url', [
                'headers' => [
                    'Authorization' => $authData['authorizationToken'],
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'json' => ['bucketId' => $bucketId],
                'timeout' => 30,
                'http_errors' => false
            ]);
            
            if ($response->getStatusCode() !== 200) {
                throw new Exception("Failed to get upload URL for {$bucketType}: " . $response->getBody()->getContents());
            }
            
            $uploadData = json_decode($response->getBody()->getContents(), true);
            if (!isset($uploadData['uploadUrl']) || !isset($uploadData['authorizationToken'])) {
                throw new Exception("Invalid upload URL response for {$bucketType}");
            }
            
            // Cache for future uploads (expires in 23 hours, B2 tokens last 24h)
            $this->uploadDataCache[$cacheKey] = $uploadData;
            
            $this->logger->debug("B2 upload data obtained successfully", $this->jobId, [
                'bucket_type' => $bucketType,
                'upload_url_domain' => parse_url($uploadData['uploadUrl'], PHP_URL_HOST)
            ]);
            
            return $uploadData;
            
        } catch (Exception $e) {
            $this->logger->error("Failed to get B2 upload data: " . $e->getMessage(), $this->jobId, [
                'bucket_type' => $bucketType
            ]);
            throw $e;
        }
    }

    /**
     * Update camera thumbnail URL in database
     */
    private function updateCameraThumbnail(array $camera, array $variations, array $uploadResults): void {
        try {
            if (!empty($uploadResults['thumbnail'])) {
                // Generate thumbnail URL similar to ScreenshotCapture
                $thumbnailUrl = $this->generateThumbnailUrl($camera['id'], 'latest.jpg');
                
                $stmt = $this->db->prepare("UPDATE cameras SET thumbnail_url = ? WHERE id = ?");
                $stmt->execute([$thumbnailUrl, $camera['id']]);
                
                $this->logger->info("Camera thumbnail URL updated successfully", $this->jobId, [
                    'camera_id' => $camera['id'],
                    'thumbnail_url' => $thumbnailUrl
                ]);
            } else {
                $this->logger->warning("No thumbnail upload result available for database update", $this->jobId, [
                    'camera_id' => $camera['id']
                ]);
            }
        } catch (Exception $e) {
            $this->logger->error("Failed to update camera thumbnail URL: " . $e->getMessage(), $this->jobId, [
                'camera_id' => $camera['id']
            ]);
            // Don't throw exception as this is not critical for screenshot capture
        }
    }
    
    /**
     * Generate thumbnail URL for Backblaze B2
     */
    private function generateThumbnailUrl(int $cameraId, string $filename): string {
        $bucketName = $this->getConfigValue('backblaze.thumbnails.bucket_name');
        $endpoint = $this->getConfigValue('backblaze.thumbnails.endpoint');
        
        if (empty($bucketName) || empty($endpoint)) {
            throw new Exception("Missing B2 configuration for thumbnail URL generation");
        }
        
        // Generate URL format: https://s3.us-west-001.backblazeb2.com/file/bucket-name/cameras/ID/filename
        return sprintf(
            '%s/file/%s/cameras/%d/%s',
            rtrim($endpoint, '/'),
            $bucketName,
            $cameraId,
            $filename
        );
    }

    /**
     * Clean up local screenshot files after successful upload
     */
    private function cleanupLocalFiles(array $variations): void {
        $cleanedCount = 0;
        
        foreach ($variations as $type => $filePath) {
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $cleanedCount++;
                    $this->logger->debug("Deleted local file: " . basename($filePath), $this->jobId);
                } else {
                    $this->logger->warning("Failed to delete local file: " . basename($filePath), $this->jobId);
                }
            }
        }
        
        $this->logger->debug("Cleaned up {$cleanedCount} local screenshot files", $this->jobId);
    }

    /**
     * Get database connection (wrapper for parent class db property)
     */
    private function getDatabase(): PDO {
        return $this->db;
    }

    /**
     * Clean up created files when total failure occurs
     */
    private function cleanupCreatedFiles(array $files): void {
        $cleanedCount = 0;
        
        foreach ($files as $file) {
            if (file_exists($file)) {
                if (unlink($file)) {
                    $cleanedCount++;
                    $this->logger->debug("Cleaned up failed file: " . basename($file), $this->jobId);
                } else {
                    $this->logger->warning("Failed to clean up file: " . basename($file), $this->jobId);
                }
            }
        }
        
        $this->logger->info("Cleaned up {$cleanedCount} files due to screenshot failure", $this->jobId);
    }

    /**
     * Log screenshot failure to database for monitoring and analysis
     */
    private function logScreenshotFailure(array $camera, int $attemptNumber, int $totalAttempts, string $failureReason, string $outputFile, string $command, array $errorOutput): void {
        try {
            $db = $this->getDatabase();
            
            $stmt = $db->prepare("
                INSERT INTO screenshot_failures 
                (camera_id, job_id, attempt_number, total_attempts, failure_reason, hls_url, output_file, file_size_bytes, command_executed, error_output, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $fileSize = file_exists($outputFile) ? filesize($outputFile) : null;
            $errorOutputText = is_array($errorOutput) ? implode("\n", $errorOutput) : (string)$errorOutput;
            
            $stmt->execute([
                $this->cameraId,
                $this->jobId,
                $attemptNumber,
                $totalAttempts,
                $failureReason,
                $camera['hls_url'] ?? null,
                basename($outputFile),
                $fileSize,
                $command,
                $errorOutputText
            ]);
            
            $this->logger->debug("Screenshot failure logged to database", $this->jobId, [
                'camera_id' => $this->cameraId,
                'attempt' => $attemptNumber,
                'reason' => substr($failureReason, 0, 100) . (strlen($failureReason) > 100 ? '...' : '')
            ]);
            
        } catch (Exception $e) {
            // Don't let database logging failures interrupt the main process
            $this->logger->warning("Failed to log screenshot failure to database: " . $e->getMessage(), $this->jobId);
        }
    }

    /**
     * Cleanup method called on destruction
     */
    public function __destruct() {
        $this->cleanupFiles($this->tempFiles);
        
        // Clear upload data cache
        $this->uploadDataCache = [];
    }
}
