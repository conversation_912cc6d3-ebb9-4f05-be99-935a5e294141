<?php

namespace App\AsyncJobs;

use Exception;
use DirectoryIterator;
use GuzzleHttp\Client;

/**
 * Video Timelapse Job Worker
 *
 * Handles asynchronous timelapse video creation from screenshots.
 * Supports multiple timelapse periods (1d, 7d, 30d) with different frame sampling strategies.
 */
class VideoJobWorker extends JobWorker {

    private string $period;
    private array $periodConfig;
    private string $outputDir;
    private string $tempDir;
    private string $screenshotsDir;
    private string $ffmpegPath;
    private Client $httpClient;
    private array $uploadDataCache = []; // Cache for B2 upload URLs and tokens
    private array $retryConfig; // Retry configuration

    public function __construct(array $jobData) {
        parent::__construct($jobData);

        // Initialize HTTP client for B2 uploads (longer timeout for large video files)
        $this->httpClient = new Client([
            'timeout' => 600, // 10 minutes for large video uploads
            'connect_timeout' => 30
        ]);

        // Load retry configuration
        $this->retryConfig = $this->getConfigValue('async_jobs.video.retry', [
            'max_attempts' => 2,
            'min_file_size_bytes' => 10000,
            'wait_between_attempts_seconds' => [30, 60],
            'cleanup_on_failure' => true
        ]);

        // Validate required configuration for video processing
        $this->validateConfig([
            'async_jobs.video.ffmpeg_path',
            'async_jobs.video.output_dir',
            'async_jobs.video.temp_dir',
            'async_jobs.video.screenshots_dir'
        ]);

        // Extract period from job config
        $this->period = $this->jobConfig['period'] ?? '1d';
        $this->periodConfig = $this->getConfigValue("async_jobs.video.periods.{$this->period}");

        if (empty($this->periodConfig)) {
            throw new Exception("Invalid or missing period configuration: {$this->period}");
        }

        // Initialize paths
        $this->outputDir = $this->getConfigValue('async_jobs.video.output_dir');
        $this->tempDir = $this->getConfigValue('async_jobs.video.temp_dir');
        $this->screenshotsDir = $this->getConfigValue('async_jobs.video.screenshots_dir');
        $this->ffmpegPath = $this->getConfigValue('async_jobs.video.ffmpeg_path');

        // Ensure directories exist
        $this->ensureDirectoryExists($this->outputDir);
        $this->ensureDirectoryExists($this->tempDir);
    }

    /**
     * Execute video timelapse generation for the assigned camera
     */
    public function execute(): array {
        $camera = $this->getCameraInfo();
        $timestamp = time();

        $this->logProgress("Starting timelapse video generation", [
            'camera_name' => $camera['name'],
            'camera_id' => $this->cameraId,
            'period' => $this->period,
            'period_name' => $this->periodConfig['name']
        ]);

        try {
            // Step 1: Collect source screenshots
            $screenshots = $this->collectSourceScreenshots($camera);

            if (empty($screenshots)) {
                throw new Exception("No screenshots found for timelapse generation");
            }

            $this->logger->info("Collected screenshots for timelapse", $this->jobId, [
                'total_screenshots' => count($screenshots),
                'period' => $this->period,
                'source_hours' => $this->periodConfig['source_hours']
            ]);

            // Step 2: Apply frame sampling strategy
            $selectedFrames = $this->applyFrameSampling($screenshots);

            $this->logger->info("Applied frame sampling", $this->jobId, [
                'original_frames' => count($screenshots),
                'selected_frames' => count($selectedFrames),
                'frame_skip' => $this->periodConfig['frame_skip']
            ]);

            // Step 3: Ensure camera output directory exists
            $this->ensureCameraOutputDirectory($camera['id']);
            
            // Step 4: Generate video filename
            $videoFilename = $this->generateVideoFilename($camera, $selectedFrames, $timestamp);
            $outputPath = $this->getCameraOutputDir($camera['id']) . '/' . $videoFilename;

            // Step 5: Create symlinks for FFmpeg input
            $inputPattern = $this->createInputSymlinks($selectedFrames);

            // Step 6: Generate timelapse video (use actual frame count from temp directory)
            $actualFrameCount = count(glob(dirname($inputPattern) . '/frame_*.jpg'));
            $videoResult = $this->generateTimelapse($inputPattern, $outputPath, $actualFrameCount);

            // Step 7: Upload to Backblaze (if enabled)
            $uploadPath = sprintf("%s/%s", $camera['id'], $videoFilename);
            $uploadResult = [];
            if ($this->getConfigValue('async_jobs.video.backblaze.enabled', false)) {
                $uploadResult = $this->uploadToBackblaze($outputPath, $uploadPath);
            } else {
                $this->logger->info("Backblaze upload disabled (safety)", $this->jobId);
            }

            // Step 8: Clean up temporary files
            $this->cleanupTempFiles($inputPattern);

            // Step 9: Clean up old videos
            $this->cleanupOldVideos($camera);

            $result = [
                'camera_id' => $this->cameraId,
                'camera_name' => $camera['name'],
                'period' => $this->period,
                'timestamp' => $timestamp,
                'video_filename' => $videoFilename,
                'video_path' => $outputPath,
                'source_images_count' => count($screenshots),
                'selected_frames_count' => count($selectedFrames),
                'actual_frames_used' => $actualFrameCount,
                'corrupted_files_skipped' => count($selectedFrames) - $actualFrameCount,
                'video_duration' => $this->calculateVideoDuration($actualFrameCount),
                'file_size' => file_exists($outputPath) ? filesize($outputPath) : 0,
                'upload_result' => $uploadResult,
                'temp_files_cleaned' => true
            ];

            $this->logProgress("Timelapse video generation completed successfully", [
                'video_filename' => $videoFilename,
                'file_size' => $result['file_size'],
                'frames_used' => count($selectedFrames)
            ]);

            return $result;

        } catch (Exception $e) {
            $this->logger->error("Timelapse generation failed: " . $e->getMessage(), $this->jobId, [
                'camera_id' => $this->cameraId,
                'period' => $this->period,
                'error_trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Collect source screenshots for video generation using efficient timestamp filtering
     *
     * @param array $camera Camera information
     * @return array List of screenshot files with timestamps
     */
    private function collectSourceScreenshots(array $camera): array {
        $cameraDir = $this->screenshotsDir . '/camera_' . $camera['id'];

        if (!is_dir($cameraDir)) {
            throw new Exception("Screenshots directory not found: {$cameraDir}");
        }

        $sourceHours = $this->periodConfig['source_hours'];
        $cutoffTimestamp = time() - ($sourceHours * 3600);

        $screenshots = [];
        $iterator = new DirectoryIterator($cameraDir);

        foreach ($iterator as $file) {
            if ($file->isDot() || !$file->isFile()) {
                continue;
            }

            $filename = $file->getFilename();

            // Extract timestamp from filename: camera_1_1234567890.jpg
            if (preg_match('/camera_\d+_(\d+)\.jpg$/', $filename, $matches)) {
                $fileTimestamp = (int)$matches[1];

                // Only include files within our timeframe
                if ($fileTimestamp >= $cutoffTimestamp) {
                    $screenshots[] = [
                        'filename' => $filename,
                        'path' => $file->getPathname(),
                        'timestamp' => $fileTimestamp,
                        'size' => $file->getSize()
                    ];
                }
            }
        }

        // Sort by timestamp (oldest first for proper timelapse sequence)
        usort($screenshots, function($a, $b) {
            return $a['timestamp'] <=> $b['timestamp'];
        });

        $this->logger->debug("Screenshot collection completed", $this->jobId, [
            'total_found' => count($screenshots),
            'cutoff_timestamp' => $cutoffTimestamp,
            'source_hours' => $sourceHours,
            'oldest_timestamp' => !empty($screenshots) ? $screenshots[0]['timestamp'] : null,
            'newest_timestamp' => !empty($screenshots) ? end($screenshots)['timestamp'] : null
        ]);

        return $screenshots;
    }

    /**
     * Apply frame sampling strategy based on period configuration
     *
     * @param array $screenshots All available screenshots
     * @return array Selected screenshots for timelapse
     */
    private function applyFrameSampling(array $screenshots): array {
        $frameSkip = $this->periodConfig['frame_skip'];

        if ($frameSkip <= 1) {
            // Use every frame when frame_skip is 1 or less
            $selected = $screenshots;
        } else {
            // Apply frame skipping: take every Nth frame where N = frame_skip
            // frame_skip = 2 means take frames 0, 2, 4, 6... (every 2nd frame)
            // frame_skip = 5 means take frames 0, 5, 10, 15... (every 5th frame)
            $selected = [];
            $totalFrames = count($screenshots);

            for ($i = 0; $i < $totalFrames; $i += $frameSkip) {
                $selected[] = $screenshots[$i];
            }
        }

        $this->logger->debug("Frame sampling applied", $this->jobId, [
            'original_count' => count($screenshots),
            'selected_count' => count($selected),
            'frame_skip_config' => $frameSkip,
            'frames_kept_ratio' => count($screenshots) > 0 ? round(count($selected) / count($screenshots) * 100, 1) . '%' : '0%'
        ]);

        return $selected;
    }

    /**
     * Generate video filename following the specified convention
     *
     * @param array $camera Camera information
     * @param array $selectedFrames Selected frames for video
     * @param int $timestamp Current timestamp
     * @return string Generated filename
     */
    private function generateVideoFilename(array $camera, array $selectedFrames, int $timestamp): string {
        $firstFrameTimestamp = $selectedFrames[0]['timestamp'];
        $lastFrameTimestamp = end($selectedFrames)['timestamp'];

        return sprintf(
            'camera_%d_%s_%d_%d.mp4',
            $camera['id'],
            $this->period,
            $firstFrameTimestamp,
            $lastFrameTimestamp
        );
    }

    /**
     * Create input files for FFmpeg input pattern
     * 
     * Note: Changed from symlinks to file copies to avoid FFmpeg issues with symlinks
     *
     * @param array $selectedFrames Selected frames for video
     * @return string Input pattern for FFmpeg
     */
    private function createInputSymlinks(array $selectedFrames): string {
        $tempInputDir = $this->tempDir . '/input_' . $this->jobId . '_' . time();
        $this->ensureDirectoryExists($tempInputDir);

        $frameNumber = 0;
        $skippedFiles = 0;
        foreach ($selectedFrames as $frame) {
            // Skip empty or corrupted source files that would break FFmpeg sequence
            if (!file_exists($frame['path']) || filesize($frame['path']) < 5000) {
                $skippedFiles++;
                $this->logger->debug("Skipping empty/corrupted source file", $this->jobId, [
                    'source_file' => $frame['path'],
                    'filename' => $frame['filename'],
                    'timestamp' => $frame['timestamp']
                ]);
                continue;
            }

            $inputPath = sprintf('%s/frame_%06d.jpg', $tempInputDir, $frameNumber);

            // Copy files instead of creating symlinks to avoid FFmpeg issues
            if (!copy($frame['path'], $inputPath)) {
                throw new Exception("Failed to copy input file: {$inputPath}");
            }

            $frameNumber++;
        }

        $this->logger->debug("Created input files", $this->jobId, [
            'temp_dir' => $tempInputDir,
            'frame_count' => $frameNumber,
            'pattern' => $tempInputDir . '/frame_%06d.jpg',
            'method' => 'copy_files',
            'skipped_corrupted_files' => $skippedFiles,
            'selected_frames_original' => count($selectedFrames),
            'frames_actually_copied' => $frameNumber
        ]);

        return $tempInputDir . '/frame_%06d.jpg';
    }

    /**
     * Generate timelapse video using FFmpeg
     *
     * @param string $inputPattern FFmpeg input pattern
     * @param string $outputPath Output video file path
     * @param int $frameCount Number of frames in the video
     * @return array Video generation results
     */
    private function generateTimelapse(string $inputPattern, string $outputPath, int $frameCount): array {
        $maxAttempts = $this->retryConfig['max_attempts'];
        $minFileSize = $this->retryConfig['min_file_size_bytes'];
        $waitTimes = $this->retryConfig['wait_between_attempts_seconds'];
        
        $camera = $this->getCameraInfo();
        $lastException = null;

        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            try {
                $this->logger->info("Starting video generation attempt {$attempt}/{$maxAttempts}", $this->jobId, [
                    'attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'output_path' => $outputPath,
                    'frame_count' => $frameCount
                ]);

                // Clean up any previous failed attempt
                if ($attempt > 1 && file_exists($outputPath)) {
                    unlink($outputPath);
                    $this->logger->info("Cleaned up failed video file from previous attempt", $this->jobId);
                }

                $result = $this->attemptVideoGeneration($inputPattern, $outputPath, $frameCount);
                
                // Validate file size
                $fileSize = $result['file_size'] ?? 0;
                if ($fileSize < $minFileSize) {
                    throw new Exception("Generated video file too small: {$fileSize} bytes (minimum: {$minFileSize})");
                }

                $this->logger->info("Video generation successful on attempt {$attempt}", $this->jobId, [
                    'attempt' => $attempt,
                    'file_size' => $fileSize,
                    'execution_time' => $result['execution_time']
                ]);

                return $result;

            } catch (Exception $e) {
                $lastException = $e;
                
                // Формируем данные для логирования
                $videoData = [
                    'period_type' => $this->period,
                    'source_frames_count' => $frameCount,
                    'expected_output_file' => $outputPath,
                    'file_size_bytes' => file_exists($outputPath) ? filesize($outputPath) : 0
                ];
                // Если попытка дошла до выполнения команды, добавляем детали
                if (isset($result) && is_array($result)) {
                    if (isset($result['command'])) $videoData['command'] = $result['command'];
                    if (isset($result['ffmpeg_output'])) $videoData['ffmpeg_output'] = $result['ffmpeg_output'];
                    if (isset($result['execution_time'])) $videoData['execution_time'] = $result['execution_time'];
                }
                $this->logVideoFailure($camera['id'], $attempt, $maxAttempts, $e, $videoData);

                $this->logger->warning("Video generation attempt {$attempt}/{$maxAttempts} failed: " . $e->getMessage(), $this->jobId, [
                    'attempt' => $attempt,
                    'max_attempts' => $maxAttempts,
                    'error' => $e->getMessage()
                ]);

                // If this isn't the last attempt, wait before retrying
                if ($attempt < $maxAttempts) {
                    $waitTime = $waitTimes[$attempt - 1] ?? $waitTimes[count($waitTimes) - 1];
                    $this->logger->info("Waiting {$waitTime} seconds before retry attempt", $this->jobId, [
                        'wait_seconds' => $waitTime,
                        'next_attempt' => $attempt + 1
                    ]);
                    sleep($waitTime);
                }
            }
        }

        // All attempts failed - cleanup if configured
        if ($this->retryConfig['cleanup_on_failure'] && file_exists($outputPath)) {
            unlink($outputPath);
            $this->logger->info("Cleaned up failed video file after all attempts", $this->jobId);
        }

        throw new Exception("Video generation failed after {$maxAttempts} attempts. Last error: " . $lastException->getMessage());
    }

    /**
     * Attempt to generate video with FFmpeg (single attempt)
     */
    private function attemptVideoGeneration(string $inputPattern, string $outputPath, int $frameCount): array {
        $commandTemplate = $this->getConfigValue('async_jobs.video.command_template');
        $framerate = $this->periodConfig['framerate'];
        $duration = $this->calculateVideoDuration($frameCount);
        $crf = $this->periodConfig['crf_quality'];

        // Build FFmpeg command
        $command = sprintf(
            $commandTemplate,
            escapeshellcmd($this->ffmpegPath),
            $framerate,
            $inputPattern,
            $crf,
            escapeshellarg($outputPath)
        );

        $startTime = microtime(true);

        // Execute FFmpeg command
        $output = [];
        $returnCode = 0;
        exec($command . ' 2>&1', $output, $returnCode);

        $executionTime = microtime(true) - $startTime;
        $outputText = implode("\n", $output);

        if ($returnCode !== 0) {
            throw new Exception("FFmpeg failed with return code {$returnCode}: {$outputText}");
        }

        if (!file_exists($outputPath)) {
            throw new Exception("Video file was not created: {$outputPath}");
        }

        $fileSize = filesize($outputPath);

        return [
            'success' => true,
            'output_file' => $outputPath,
            'file_size' => $fileSize,
            'execution_time' => $executionTime,
            'calculated_duration' => $duration,
            'ffmpeg_output' => $outputText,
            'command' => $command
        ];
    }

    /**
     * Upload video to Backblaze B2 storage
     *
     * @param string $videoFile Local video file path
     * @param string $filename Remote filename
     * @return array Upload results
     */
    private function uploadToBackblaze(string $videoFile, string $filename): array {
        try {
            if (!file_exists($videoFile)) {
                throw new Exception("Video file not found: " . $videoFile);
            }

            $fileSize = filesize($videoFile);
            $folderPrefix = $this->getConfigValue('async_jobs.video.backblaze.folder_prefix', 'timelapses/');
            $remotePath = $folderPrefix . $filename;

            $this->logger->info("Starting Backblaze video upload", $this->jobId, [
                'local_file' => basename($videoFile),
                'remote_path' => $remotePath,
                'file_size' => $fileSize
            ]);

            // Upload to video archive bucket
            $uploadResult = $this->uploadVideoFile($videoFile, $remotePath, 'video-archive');

            $this->logger->info("Backblaze video upload completed successfully", $this->jobId, [
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $uploadResult['file_id'] ?? 'unknown'
            ]);

            return [
                'success' => true,
                'bucket_type' => 'video-archive',
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $uploadResult['file_id'] ?? null,
                'upload_timestamp' => $uploadResult['upload_timestamp'] ?? null,
                'uploaded_at' => date('Y-m-d H:i:s')
            ];

        } catch (Exception $e) {
            $this->logger->error("Backblaze video upload failed: " . $e->getMessage(), $this->jobId, [
                'local_file' => basename($videoFile),
                'filename' => $filename,
                'error_trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'file_size' => file_exists($videoFile) ? filesize($videoFile) : 0
            ];
        }
    }

    /**
     * Upload single video file to Backblaze B2
     */
    private function uploadVideoFile(string $localFile, string $remotePath, string $bucketType): array {
        try {
            // Get upload data for the specified bucket type
            $uploadData = $this->getVideoUploadData($bucketType);
            
            // Read file content and calculate SHA1
            $fileContent = file_get_contents($localFile);
            if ($fileContent === false) {
                throw new Exception("Failed to read video file: " . $localFile);
            }
            
            $sha1 = sha1($fileContent);
            $contentType = 'video/mp4';
            $fileSize = strlen($fileContent);
            
            $this->logger->info("Starting B2 video upload", $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'content_type' => $contentType,
                'upload_url' => substr($uploadData['uploadUrl'], 0, 50) . '...'
            ]);
            
            // Upload to Backblaze B2 with extended timeout for large video files
            $response = $this->httpClient->post($uploadData['uploadUrl'], [
                'headers' => [
                    'Authorization' => $uploadData['authorizationToken'],
                    'X-Bz-File-Name' => $remotePath,
                    'Content-Type' => $contentType,
                    'X-Bz-Content-Sha1' => $sha1,
                    'Content-Length' => $fileSize
                ],
                'body' => $fileContent,
                'timeout' => 1800, // 30 minutes timeout for large video files
                'http_errors' => false
            ]);
            
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            
            if ($statusCode !== 200) {
                throw new Exception("B2 video upload failed with status {$statusCode}: " . $responseBody);
            }
            
            $responseData = json_decode($responseBody, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Failed to decode B2 response: " . json_last_error_msg());
            }
            
            $this->logger->info("B2 video upload completed successfully", $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $responseData['fileId'] ?? 'unknown'
            ]);
            
            return [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'file_size' => $fileSize,
                'file_id' => $responseData['fileId'] ?? null,
                'upload_timestamp' => $responseData['uploadTimestamp'] ?? null,
                'uploaded_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            $this->logger->error("B2 video upload failed: " . $e->getMessage(), $this->jobId, [
                'bucket_type' => $bucketType,
                'remote_path' => $remotePath,
                'local_file' => basename($localFile)
            ]);
            throw $e;
        }
    }

    /**
     * Get upload data (URL and token) for video bucket type
     */
    private function getVideoUploadData(string $bucketType): array {
        $cacheKey = "b2_video_upload_data_{$bucketType}";
        
        // Check if we have cached upload data
        if (isset($this->uploadDataCache[$cacheKey])) {
            return $this->uploadDataCache[$cacheKey];
        }
        
        try {
            // Get B2 credentials for video bucket
            $keyId = trim($this->getConfigValue("backblaze.{$bucketType}.application_key_id"));
            $key = trim($this->getConfigValue("backblaze.{$bucketType}.application_key"));
            $bucketId = trim($this->getConfigValue("backblaze.{$bucketType}.bucket_id"));
            
            if (empty($keyId) || empty($key) || empty($bucketId)) {
                throw new Exception("Missing B2 credentials for {$bucketType} bucket");
            }
            
            // Authorize account
            $authHeader = 'Basic ' . base64_encode($keyId . ':' . $key);
            
            $response = $this->httpClient->get('https://api.backblazeb2.com/b2api/v2/b2_authorize_account', [
                'headers' => [
                    'Authorization' => $authHeader,
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'timeout' => 60,
                'http_errors' => false
            ]);
            
            if ($response->getStatusCode() !== 200) {
                throw new Exception("B2 authorization failed for {$bucketType}: " . $response->getBody()->getContents());
            }
            
            $authData = json_decode($response->getBody()->getContents(), true);
            if (!isset($authData['authorizationToken']) || !isset($authData['apiUrl'])) {
                throw new Exception("Invalid B2 authorization response for {$bucketType}");
            }
            
            // Get upload URL
            $response = $this->httpClient->post($authData['apiUrl'] . '/b2api/v2/b2_get_upload_url', [
                'headers' => [
                    'Authorization' => $authData['authorizationToken'],
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json'
                ],
                'json' => ['bucketId' => $bucketId],
                'timeout' => 60,
                'http_errors' => false
            ]);
            
            if ($response->getStatusCode() !== 200) {
                throw new Exception("Failed to get upload URL for {$bucketType}: " . $response->getBody()->getContents());
            }
            
            $uploadData = json_decode($response->getBody()->getContents(), true);
            if (!isset($uploadData['uploadUrl']) || !isset($uploadData['authorizationToken'])) {
                throw new Exception("Invalid upload URL response for {$bucketType}");
            }
            
            // Cache for future uploads (expires in 23 hours, B2 tokens last 24h)
            $this->uploadDataCache[$cacheKey] = $uploadData;
            
            $this->logger->debug("B2 video upload data obtained successfully", $this->jobId, [
                'bucket_type' => $bucketType,
                'upload_url_domain' => parse_url($uploadData['uploadUrl'], PHP_URL_HOST)
            ]);
            
            return $uploadData;
            
        } catch (Exception $e) {
            $this->logger->error("Failed to get B2 video upload data: " . $e->getMessage(), $this->jobId, [
                'bucket_type' => $bucketType
            ]);
            throw $e;
        }
    }

    /**
     * Update database with video information
     * 
     * @param array $camera Camera information
     * @param array $videoResults Video generation results
     */
    private function updateVideoDatabase(array $camera, array $videoResults): void {
        // TODO: Implement database update logic
        // This would store video URLs and metadata in the database
    }

    /**
     * Calculate video duration based on frame count and framerate
     *
     * @param int $frameCount Number of frames in the video
     * @return float Calculated duration in seconds
     */
    private function calculateVideoDuration(int $frameCount): float {
        $framerate = $this->periodConfig['framerate'];
        
        if ($framerate <= 0 || $frameCount <= 0) {
            return 1.0; // Fallback to 1 second minimum
        }
        
        $duration = $frameCount / $framerate;
        
        // Ensure minimum duration of 0.1 seconds
        return max(0.1, $duration);
    }

    /**
     * Get camera-specific output directory path
     *
     * @param int $cameraId Camera ID
     * @return string Camera output directory path
     */
    private function getCameraOutputDir(int $cameraId): string {
        return $this->outputDir . '/camera_' . $cameraId;
    }

    /**
     * Ensure camera-specific output directory exists
     *
     * @param int $cameraId Camera ID
     * @return void
     */
    private function ensureCameraOutputDirectory(int $cameraId): void {
        $cameraOutputDir = $this->getCameraOutputDir($cameraId);
        
        if (!is_dir($cameraOutputDir)) {
            $this->ensureDirectoryExists($cameraOutputDir);
            
            $this->logger->debug("Created camera output directory", $this->jobId, [
                'camera_id' => $cameraId,
                'directory' => $cameraOutputDir
            ]);
        }
    }

    /**
     * Clean up temporary files and directories
     *
     * @param string $inputPattern FFmpeg input pattern (contains temp directory)
     * @return void
     */
    private function cleanupTempFiles(string $inputPattern): void {
        // Extract temp directory from input pattern
        $tempDir = dirname($inputPattern);

        if (strpos($tempDir, $this->tempDir) === 0 && is_dir($tempDir)) {
            $this->logger->debug("Cleaning up temporary directory", $this->jobId, [
                'temp_dir' => $tempDir
            ]);

            // Remove all files in temp directory
            $files = glob($tempDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                } elseif (is_link($file)) {
                    unlink($file); // Remove symlinks
                }
            }

            // Remove the directory itself
            rmdir($tempDir);

            $this->logger->debug("Temporary files cleaned up", $this->jobId, [
                'files_removed' => count($files),
                'temp_dir_removed' => $tempDir
            ]);
        }
    }

    /**
     * Log video generation failure to database
     */
    private function logVideoFailure(int $cameraId, int $attemptNumber, int $totalAttempts, Exception $e, array $videoData): void {
        try {
            $db = $this->getDatabase();
            
            // Determine failure reason based on error message
            $errorMsg = $e->getMessage();
            $failureReason = 'unknown';
            
            if (strpos($errorMsg, 'FFmpeg failed') !== false) {
                $failureReason = 'ffmpeg_failed';
            } elseif (strpos($errorMsg, 'file was not created') !== false) {
                $failureReason = 'file_not_created';
            } elseif (strpos($errorMsg, 'too small') !== false) {
                $failureReason = 'file_too_small';
            } elseif (strpos($errorMsg, 'No screenshots found') !== false) {
                $failureReason = 'no_source_frames';
            } elseif (strpos($errorMsg, 'directory not found') !== false) {
                $failureReason = 'directory_not_found';
            } elseif (strpos($errorMsg, 'symlink') !== false) {
                $failureReason = 'symlink_creation_failed';
            } elseif (strpos($errorMsg, 'upload') !== false) {
                $failureReason = 'upload_failed';
            }

            // Extract FFmpeg details if available
            $ffmpegCommand = null;
            $ffmpegOutput = null;
            $executionTime = null;
            
            if (isset($videoData['command'])) {
                $ffmpegCommand = $videoData['command'];
            }
            if (isset($videoData['ffmpeg_output'])) {
                $ffmpegOutput = $videoData['ffmpeg_output'];
            }
            if (isset($videoData['execution_time'])) {
                $executionTime = $videoData['execution_time'];
            }

            $stmt = $db->prepare("
                INSERT INTO video_failures (
                    camera_id, job_id, attempt_number, total_attempts, failure_reason,
                    period_type, source_frames_count, expected_output_file, file_size_bytes,
                    ffmpeg_command, ffmpeg_output, error_message, execution_time_seconds
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $cameraId,
                $this->jobId,
                $attemptNumber,
                $totalAttempts,
                $failureReason,
                $videoData['period_type'] ?? $this->period,
                $videoData['source_frames_count'] ?? null,
                $videoData['expected_output_file'] ?? null,
                $videoData['file_size_bytes'] ?? 0,
                $ffmpegCommand,
                $ffmpegOutput,
                substr($errorMsg, 0, 2000), // Truncate if too long
                $executionTime
            ]);

            $this->logger->info("Logged video failure to database", $this->jobId, [
                'camera_id' => $cameraId,
                'attempt' => $attemptNumber,
                'failure_reason' => $failureReason,
                'failure_id' => $db->lastInsertId()
            ]);

        } catch (Exception $logError) {
            $this->logger->error("Failed to log video failure to database: " . $logError->getMessage(), $this->jobId, [
                'original_error' => $e->getMessage(),
                'camera_id' => $cameraId,
                'attempt' => $attemptNumber
            ]);
        }
    }

    /**
     * Get database connection (wrapper for consistent access)
     */
    private function getDatabase(): \PDO {
        return $this->database;
    }

    /**
     * Clean up old videos based on retention policy
     *
     * @param array $camera Camera information
     * @return void
     */
    private function cleanupOldVideos(array $camera): void {
        $retentionDays = $this->periodConfig['old_videos_days'];
        $cutoffTimestamp = time() - ($retentionDays * 24 * 3600);

        $cameraOutputDir = $this->getCameraOutputDir($camera['id']);
        $pattern = sprintf(
            '%s/camera_%d_%s_*.mp4',
            $cameraOutputDir,
            $camera['id'],
            $this->period
        );

        $videoFiles = glob($pattern);
        $removedCount = 0;

        foreach ($videoFiles as $videoFile) {
            $filename = basename($videoFile);

            // Extract timestamp from filename: camera_1_1d_1234567890_1234567890.mp4
            // Use lastFrameTimestamp (second timestamp) for more accurate age calculation
            if (preg_match('/camera_\d+_[^_]+_\d+_(\d+)\.mp4$/', $filename, $matches)) {
                $videoTimestamp = (int)$matches[1]; // Now captures lastFrameTimestamp

                if ($videoTimestamp < $cutoffTimestamp) {
                    if (unlink($videoFile)) {
                        $removedCount++;
                        $this->logger->debug("Removed old video", $this->jobId, [
                            'filename' => $filename,
                            'timestamp' => $videoTimestamp,
                            'age_days' => round((time() - $videoTimestamp) / 86400, 1),
                            'timestamp_type' => 'lastFrame'
                        ]);
                    }
                }
            }
        }

        if ($removedCount > 0) {
            $this->logger->info("Old videos cleanup completed", $this->jobId, [
                'removed_count' => $removedCount,
                'retention_days' => $retentionDays,
                'period' => $this->period
            ]);
        }
    }

    /**
     * Cleanup method called on destruction
     */
    public function __destruct() {
        // Clear upload data cache
        $this->uploadDataCache = [];
    }

}
